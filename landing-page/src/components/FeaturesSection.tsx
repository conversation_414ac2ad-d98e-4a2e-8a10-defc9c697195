'use client'

import { motion } from 'framer-motion'
import { 
  Shield, 
  Zap, 
  Users, 
  Wifi, 
  Battery, 
  Lock, 
  Eye, 
  MessageCircle,
  Network,
  Smartphone
} from 'lucide-react'

const FeaturesSection = () => {
  const features = [
    {
      icon: Network,
      title: 'Mesh Networking',
      description: 'Multi-hop routing with TTL-based forwarding. Connect up to 20 peers simultaneously with automatic discovery via Bluetooth LE.',
      details: ['Automatic peer discovery', 'Multi-hop routing (max 7 hops)', 'Connection management', 'Loop prevention'],
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Shield,
      title: 'End-to-End Encryption',
      description: 'Military-grade encryption with X25519 key exchange, AES-256-GCM, and Ed25519 signatures for message authenticity.',
      details: ['X25519 key exchange', 'AES-256-GCM encryption', 'Ed25519 signatures', 'Forward secrecy'],
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Zap,
      title: 'Zero-Knowledge Proofs',
      description: 'Anonymous authentication and private reputation using Noir ZK circuits. Prove membership without revealing identity.',
      details: ['Anonymous authentication', 'Private reputation', 'Selective disclosure', 'Group authorization'],
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: MessageCircle,
      title: 'Store-and-Forward',
      description: 'Offline message delivery with intelligent routing. Messages reach their destination even when peers are temporarily offline.',
      details: ['Offline message delivery', 'Message persistence', 'Intelligent routing', 'Delivery confirmation'],
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Eye,
      title: 'Privacy Features',
      description: 'Cover traffic generation and timing randomization prevent traffic analysis. Emergency wipe capability for security.',
      details: ['Cover traffic generation', 'Timing randomization', 'Ephemeral identities', 'Emergency wipe'],
      color: 'from-indigo-500 to-purple-500'
    },
    {
      icon: Battery,
      title: 'Battery Optimization',
      description: '4-tier power management with adaptive connection limits. LZ4 compression saves 30-70% bandwidth.',
      details: ['4-tier power management', 'Adaptive connections', 'LZ4 compression', 'Binary protocol'],
      color: 'from-red-500 to-pink-500'
    }
  ]

  return (
    <section className="py-20 bg-dark-900 relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Built for <span className="gradient-text">Privacy</span> & <span className="gradient-text">Performance</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            KRTR Mesh combines cutting-edge cryptography with mesh networking to deliver 
            secure, private, and resilient communication that works anywhere.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105">
                {/* Icon */}
                <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${feature.color} p-4 mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-8 h-8 text-white" />
                </div>

                {/* Title */}
                <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors">
                  {feature.title}
                </h3>

                {/* Description */}
                <p className="text-gray-400 mb-6 leading-relaxed">
                  {feature.description}
                </p>

                {/* Details */}
                <ul className="space-y-2">
                  {feature.details.map((detail, detailIndex) => (
                    <li key={detailIndex} className="flex items-center text-sm text-gray-500">
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3" />
                      {detail}
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Stats section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {[
            { value: '7', label: 'Max Hops', suffix: '' },
            { value: '20', label: 'Max Connections', suffix: '' },
            { value: '70', label: 'Bandwidth Savings', suffix: '%' },
            { value: '256', label: 'AES Encryption', suffix: '-bit' },
          ].map((stat, index) => (
            <div key={stat.label} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                {stat.value}<span className="text-blue-400">{stat.suffix}</span>
              </div>
              <div className="text-gray-400 text-sm">{stat.label}</div>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default FeaturesSection
