'use client'

import { motion } from 'framer-motion'
import { Github, Twitter, Mail, Wifi, Shield, Zap } from 'lucide-react'

const Footer = () => {
  const footerLinks = {
    Product: [
      { name: 'Features', href: '#features' },
      { name: 'Technology', href: '#tech' },
      { name: 'Download', href: '#download' },
      { name: 'Roadmap', href: '#roadmap' }
    ],
    Developers: [
      { name: 'Documentation', href: '#docs' },
      { name: 'API Reference', href: '#api' },
      { name: 'GitHub', href: 'https://github.com/Z0rlord/krtr-mesh' },
      { name: 'Contributing', href: '#contributing' }
    ],
    Community: [
      { name: 'Discord', href: '#discord' },
      { name: 'Twitter', href: '#twitter' },
      { name: 'Blog', href: '#blog' },
      { name: 'Support', href: '#support' }
    ],
    Legal: [
      { name: 'Privacy Policy', href: '#privacy' },
      { name: 'Terms of Service', href: '#terms' },
      { name: 'Security', href: '#security' },
      { name: 'License', href: '#license' }
    ]
  }

  return (
    <footer className="bg-dark-900 border-t border-dark-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-12">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-4"
            >
              {/* Logo */}
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Wifi className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-2xl font-bold font-mono text-white">
                  krtr<span className="text-blue-400">*</span>mesh
                </h3>
              </div>

              {/* Description */}
              <p className="text-gray-400 leading-relaxed max-w-sm">
                Decentralized, encrypted, offline-first messaging for the post-platform era. 
                Built from the blackout. Inspired by the streets.
              </p>

              {/* Key Features Icons */}
              <div className="flex space-x-4">
                <div className="flex items-center space-x-2 text-green-400">
                  <Shield className="w-4 h-4" />
                  <span className="text-xs">Encrypted</span>
                </div>
                <div className="flex items-center space-x-2 text-yellow-400">
                  <Zap className="w-4 h-4" />
                  <span className="text-xs">Zero-Knowledge</span>
                </div>
                <div className="flex items-center space-x-2 text-blue-400">
                  <Wifi className="w-4 h-4" />
                  <span className="text-xs">Mesh Network</span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Links Sections */}
          {Object.entries(footerLinks).map(([category, links], index) => (
            <motion.div
              key={category}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="space-y-4"
            >
              <h4 className="text-white font-semibold">{category}</h4>
              <ul className="space-y-2">
                {links.map((link) => (
                  <li key={link.name}>
                    <a 
                      href={link.href}
                      className="text-gray-400 hover:text-white transition-colors text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Newsletter Signup */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="bg-dark-800 border border-dark-700 rounded-2xl p-8 mb-12"
        >
          <div className="text-center max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">Stay Updated</h3>
            <p className="text-gray-400 mb-6">
              Get the latest updates on KRTR Mesh development, security advisories, 
              and community news delivered to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input 
                type="email" 
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
              />
              <button className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300">
                Subscribe
              </button>
            </div>
          </div>
        </motion.div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="flex flex-col md:flex-row justify-between items-center pt-8 border-t border-dark-700"
        >
          {/* Copyright */}
          <div className="text-gray-400 text-sm mb-4 md:mb-0">
            © 2024 KRTR Team. Built for the people, by the people.
          </div>

          {/* Social Links */}
          <div className="flex space-x-6">
            {[
              { icon: Github, href: 'https://github.com/Z0rlord/krtr-mesh', label: 'GitHub' },
              { icon: Twitter, href: '#twitter', label: 'Twitter' },
              { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' }
            ].map((social) => (
              <a
                key={social.label}
                href={social.href}
                className="text-gray-400 hover:text-white transition-colors"
                aria-label={social.label}
              >
                <social.icon className="w-5 h-5" />
              </a>
            ))}
          </div>
        </motion.div>

        {/* Tagline */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.7 }}
          viewport={{ once: true }}
          className="text-center mt-8"
        >
          <p className="text-gray-500 text-sm font-mono">
            🕳️ We build from the edge.
          </p>
          <p className="text-gray-600 text-xs mt-2 italic">
            "Decentralized by design. Privacy by default. Built for the people."
          </p>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
