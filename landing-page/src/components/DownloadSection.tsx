'use client'

import { motion } from 'framer-motion'
import { Download, Github, ExternalLink, Smartphone, Monitor, Tablet } from 'lucide-react'

const DownloadSection = () => {
  const platforms = [
    {
      name: 'iOS',
      icon: Smartphone,
      description: 'Native iOS app with full mesh networking capabilities',
      status: 'Coming Soon',
      statusColor: 'bg-yellow-500',
      link: '#',
      features: ['Native Swift UI', 'Background mesh', 'Biometric unlock', 'AirDrop integration']
    },
    {
      name: 'Android',
      icon: Tablet,
      description: 'Android app with Nearby Connections API integration',
      status: 'In Development',
      statusColor: 'bg-blue-500',
      link: '#',
      features: ['Material Design', 'Background sync', 'Fingerprint auth', 'Quick share']
    },
    {
      name: 'macOS',
      icon: Monitor,
      description: 'Desktop app for mesh coordination and development',
      status: 'Beta Available',
      statusColor: 'bg-green-500',
      link: '#',
      features: ['Native macOS', 'Developer tools', 'Network analysis', 'Bridge mode']
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-dark-800 to-dark-900 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-40 h-40 bg-blue-500 rounded-full blur-3xl" />
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-purple-500 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Get <span className="gradient-text">KRTR Mesh</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Join the decentralized communication revolution. Download KRTR Mesh 
            and start building resilient networks today.
          </p>
        </motion.div>

        {/* Platform Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {platforms.map((platform, index) => (
            <motion.div
              key={platform.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105">
                {/* Platform Icon & Status */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                      <platform.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white">{platform.name}</h3>
                  </div>
                  <span className={`px-3 py-1 ${platform.statusColor} text-white text-xs font-semibold rounded-full`}>
                    {platform.status}
                  </span>
                </div>

                {/* Description */}
                <p className="text-gray-400 mb-6 leading-relaxed">
                  {platform.description}
                </p>

                {/* Features */}
                <div className="space-y-2 mb-8">
                  {platform.features.map((feature, featureIndex) => (
                    <div key={feature} className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full" />
                      <span className="text-sm text-gray-300">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Download Button */}
                <button 
                  className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 group-hover:scale-105"
                  disabled={platform.status === 'Coming Soon'}
                >
                  <Download className="w-4 h-4" />
                  <span>
                    {platform.status === 'Coming Soon' ? 'Notify Me' : 
                     platform.status === 'In Development' ? 'Join Beta' : 'Download'}
                  </span>
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Open Source Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="bg-dark-800 border border-dark-700 rounded-2xl p-8 text-center"
        >
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
              <Github className="w-8 h-8 text-white" />
            </div>
          </div>

          <h3 className="text-2xl font-bold text-white mb-4">Open Source & Transparent</h3>
          <p className="text-gray-400 mb-8 max-w-2xl mx-auto">
            KRTR Mesh is built in the open. Audit our code, contribute features, 
            or build your own mesh applications using our protocols.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="px-6 py-3 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300 flex items-center justify-center space-x-2">
              <Github className="w-4 h-4" />
              <span>View Source</span>
            </button>
            <button className="px-6 py-3 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300 flex items-center justify-center space-x-2">
              <ExternalLink className="w-4 h-4" />
              <span>Documentation</span>
            </button>
          </div>
        </motion.div>

        {/* Early Access CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border border-blue-500/30 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-white mb-4">Join the Beta Program</h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Get early access to KRTR Mesh, help shape the future of decentralized communication, 
              and be part of the privacy revolution.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <input 
                type="email" 
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
              />
              <button className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300">
                Join Beta
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default DownloadSection
