'use client'

import { motion } from 'framer-motion'
import { 
  MessageCircle, 
  Network, 
  Shield, 
  BarChart3, 
  Users, 
  Settings,
  Zap,
  Lock
} from 'lucide-react'

const AppShowcase = () => {
  const appScreens = [
    {
      title: 'Dashboard',
      description: 'Real-time network status, connection metrics, and quick actions all in one place.',
      icon: BarChart3,
      features: ['Network Status', 'Connected Peers', 'Battery Level', 'Power Mode'],
      mockup: {
        header: 'Dashboard',
        content: [
          { label: 'Network Status', value: 'Active', status: 'online' },
          { label: 'Connected Peers', value: '7', status: 'normal' },
          { label: 'Battery Level', value: '85%', status: 'good' },
          { label: 'Power Mode', value: 'Balanced', status: 'normal' }
        ]
      }
    },
    {
      title: 'Mesh Network',
      description: 'Visualize your mesh connections, peer discovery, and network topology in real-time.',
      icon: Network,
      features: ['Peer Discovery', 'Connection Map', 'Signal Strength', 'Routing Info'],
      mockup: {
        header: 'Mesh Network',
        content: [
          { label: 'alice@mesh', value: 'Connected', status: 'online' },
          { label: 'bob@mesh', value: 'Discovering', status: 'pending' },
          { label: 'charlie@mesh', value: 'Connected', status: 'online' },
          { label: 'diana@mesh', value: 'Offline', status: 'offline' }
        ]
      }
    },
    {
      title: 'Secure Chat',
      description: 'End-to-end encrypted messaging with forward secrecy and anonymous authentication.',
      icon: MessageCircle,
      features: ['E2E Encryption', 'Group Chats', 'File Sharing', 'Message Status'],
      mockup: {
        header: 'General Chat',
        content: [
          { label: 'alice', value: 'Hey everyone! 👋', status: 'received' },
          { label: 'bob', value: 'Network is looking good today', status: 'received' },
          { label: 'You', value: 'Agreed! Zero issues so far', status: 'sent' },
          { label: 'charlie', value: 'Love the new ZK features', status: 'received' }
        ]
      }
    },
    {
      title: 'ZK Features',
      description: 'Zero-knowledge proofs for anonymous authentication and private reputation systems.',
      icon: Zap,
      features: ['Anonymous Auth', 'Private Reputation', 'Proof Generation', 'Verification'],
      mockup: {
        header: 'ZK Dashboard',
        content: [
          { label: 'Identity Proof', value: 'Generated', status: 'online' },
          { label: 'Reputation Score', value: 'Hidden', status: 'private' },
          { label: 'Group Membership', value: 'Verified', status: 'online' },
          { label: 'Anonymous Mode', value: 'Active', status: 'private' }
        ]
      }
    }
  ]

  return (
    <section className="py-20 bg-dark-900 relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-900/10 to-purple-900/10" />

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Experience <span className="gradient-text">KRTR Mesh</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Simple, intuitive interfaces that put powerful mesh networking and 
            cryptographic privacy at your fingertips.
          </p>
        </motion.div>

        {/* App Screens Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {appScreens.map((screen, index) => (
            <motion.div
              key={screen.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="flex flex-col lg:flex-row items-center gap-8">
                {/* Screen Info */}
                <div className="flex-1 space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                      <screen.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white">{screen.title}</h3>
                  </div>

                  <p className="text-gray-400 leading-relaxed">
                    {screen.description}
                  </p>

                  <div className="grid grid-cols-2 gap-3">
                    {screen.features.map((feature, featureIndex) => (
                      <div key={feature} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-400 rounded-full" />
                        <span className="text-sm text-gray-300">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Phone Mockup */}
                <div className="flex-shrink-0">
                  <div className="w-64 h-96 bg-dark-800 rounded-3xl border-4 border-dark-700 p-4 group-hover:border-blue-500/50 transition-all duration-300">
                    {/* Phone header */}
                    <div className="bg-dark-700 rounded-2xl h-full p-4 space-y-4">
                      {/* Status bar */}
                      <div className="flex justify-between items-center text-xs text-gray-400">
                        <span>9:41</span>
                        <div className="flex space-x-1">
                          <div className="w-4 h-2 bg-green-400 rounded-sm" />
                          <div className="w-4 h-2 bg-blue-400 rounded-sm" />
                          <div className="w-4 h-2 bg-gray-600 rounded-sm" />
                        </div>
                      </div>

                      {/* Header */}
                      <div className="text-center">
                        <h4 className="text-white font-semibold">{screen.mockup.header}</h4>
                      </div>

                      {/* Content */}
                      <div className="space-y-3 flex-1">
                        {screen.mockup.content.map((item, itemIndex) => (
                          <div key={itemIndex} className="flex justify-between items-center p-2 bg-dark-600 rounded-lg">
                            <span className="text-gray-300 text-sm">{item.label}</span>
                            <div className="flex items-center space-x-2">
                              <span className="text-white text-sm">{item.value}</span>
                              <div className={`w-2 h-2 rounded-full ${
                                item.status === 'online' ? 'bg-green-400' :
                                item.status === 'offline' ? 'bg-red-400' :
                                item.status === 'pending' ? 'bg-yellow-400' :
                                item.status === 'private' ? 'bg-purple-400' :
                                item.status === 'sent' ? 'bg-blue-400' :
                                item.status === 'received' ? 'bg-gray-400' :
                                'bg-gray-500'
                              }`} />
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Bottom indicator */}
                      <div className="flex justify-center">
                        <div className="w-8 h-1 bg-gray-600 rounded-full" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Platform Support */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-20 text-center"
        >
          <h3 className="text-2xl font-bold text-white mb-8">Available on All Platforms</h3>
          <div className="flex justify-center space-x-8">
            {[
              { name: 'iOS', icon: '📱' },
              { name: 'Android', icon: '🤖' },
              { name: 'macOS', icon: '💻' },
            ].map((platform) => (
              <div key={platform.name} className="flex flex-col items-center space-y-2">
                <div className="text-4xl">{platform.icon}</div>
                <span className="text-gray-400">{platform.name}</span>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default AppShowcase
