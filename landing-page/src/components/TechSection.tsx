'use client'

import { motion } from 'framer-motion'
import { Code, Database, Shield, Zap, Cpu, Layers } from 'lucide-react'

const TechSection = () => {
  const techStack = [
    {
      category: 'Networking',
      icon: Layers,
      color: 'from-blue-500 to-cyan-500',
      technologies: [
        { name: 'Bluetooth LE Mesh', description: 'Low-energy mesh networking' },
        { name: 'Multi-hop Routing', description: 'TTL-based message forwarding' },
        { name: 'Store-and-Forward', description: 'Offline message delivery' },
        { name: 'Connection Management', description: 'Adaptive peer limits' }
      ]
    },
    {
      category: 'Cryptography',
      icon: Shield,
      color: 'from-green-500 to-emerald-500',
      technologies: [
        { name: 'X25519 ECDH', description: 'Key exchange with forward secrecy' },
        { name: 'AES-256-GCM', description: 'Authenticated encryption' },
        { name: 'Ed25519', description: 'Digital signatures' },
        { name: 'Argon2id', description: 'Password-based key derivation' }
      ]
    },
    {
      category: 'Zero-Knowledge',
      icon: Zap,
      color: 'from-yellow-500 to-orange-500',
      technologies: [
        { name: 'Noir Circuits', description: 'ZK proof generation' },
        { name: 'Anonymous Auth', description: 'Identity-preserving verification' },
        { name: 'Private Reputation', description: 'Trust without exposure' },
        { name: 'Selective Disclosure', description: 'Prove without revealing' }
      ]
    },
    {
      category: 'Performance',
      icon: Cpu,
      color: 'from-purple-500 to-pink-500',
      technologies: [
        { name: 'LZ4 Compression', description: '30-70% bandwidth savings' },
        { name: 'Binary Protocol', description: 'Efficient message encoding' },
        { name: 'Battery Optimization', description: '4-tier power management' },
        { name: 'Message Fragmentation', description: 'Large payload handling' }
      ]
    }
  ]

  const architectureLayers = [
    { name: 'Application Layer', description: 'SwiftUI / React Native UI', color: 'bg-blue-500' },
    { name: 'Service Layer', description: 'Mesh, Crypto, ZK Services', color: 'bg-green-500' },
    { name: 'Protocol Layer', description: 'KRTR Binary Protocol', color: 'bg-yellow-500' },
    { name: 'Transport Layer', description: 'Bluetooth LE Abstraction', color: 'bg-purple-500' },
    { name: 'Hardware Layer', description: 'iOS / Android / macOS', color: 'bg-red-500' }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-dark-800 to-dark-900 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-32 h-32 bg-blue-500 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-purple-500 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            <span className="gradient-text">Technical</span> Architecture
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Built on proven cryptographic primitives and modern networking protocols, 
            KRTR Mesh delivers enterprise-grade security with consumer-friendly usability.
          </p>
        </motion.div>

        {/* Tech Stack Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
          {techStack.map((category, index) => (
            <motion.div
              key={category.category}
              initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-dark-800 border border-dark-700 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300"
            >
              <div className="flex items-center mb-6">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${category.color} p-3 mr-4`}>
                  <category.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">{category.category}</h3>
              </div>

              <div className="space-y-4">
                {category.technologies.map((tech, techIndex) => (
                  <div key={tech.name} className="flex justify-between items-start">
                    <div>
                      <div className="text-white font-semibold">{tech.name}</div>
                      <div className="text-gray-400 text-sm">{tech.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Architecture Diagram */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-dark-800 border border-dark-700 rounded-2xl p-8"
        >
          <h3 className="text-2xl font-bold text-white mb-8 text-center">System Architecture</h3>
          
          <div className="space-y-4">
            {architectureLayers.map((layer, index) => (
              <motion.div
                key={layer.name}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center p-4 bg-dark-700 rounded-xl hover:bg-dark-600 transition-colors"
              >
                <div className={`w-4 h-4 ${layer.color} rounded-full mr-4`} />
                <div className="flex-1">
                  <div className="text-white font-semibold">{layer.name}</div>
                  <div className="text-gray-400 text-sm">{layer.description}</div>
                </div>
                <Code className="w-5 h-5 text-gray-500" />
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Security Highlights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {[
            {
              title: 'Forward Secrecy',
              description: 'New keys generated each session ensure past communications remain secure even if current keys are compromised.',
              icon: Shield
            },
            {
              title: 'Zero Trust',
              description: 'No central servers or authorities. Every message is cryptographically verified and authenticated.',
              icon: Database
            },
            {
              title: 'Privacy by Design',
              description: 'Cover traffic, timing randomization, and ephemeral identities prevent traffic analysis.',
              icon: Zap
            }
          ].map((highlight, index) => (
            <div key={highlight.title} className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                <highlight.icon className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-xl font-bold text-white mb-2">{highlight.title}</h4>
              <p className="text-gray-400 text-sm leading-relaxed">{highlight.description}</p>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default TechSection
