import type { Metadata } from 'next'
import { Inter, JetBrains_Mono } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' })
const jetbrainsMono = JetBrains_Mono({ 
  subsets: ['latin'], 
  variable: '--font-mono' 
})

export const metadata: Metadata = {
  title: 'KRTR Mesh - Decentralized Encrypted Messaging',
  description: 'Decentralized, encrypted, offline-first messaging for the post-platform era. Built from the blackout. Inspired by the streets.',
  keywords: [
    'mesh networking',
    'decentralized messaging',
    'privacy',
    'encryption',
    'zero-knowledge',
    'offline-first',
    'bluetooth',
    'peer-to-peer'
  ],
  authors: [{ name: 'KRTR Team' }],
  creator: 'KRTR Team',
  publisher: 'KRTR Team',
  openGraph: {
    title: 'KRTR Mesh - Decentralized Encrypted Messaging',
    description: 'Decentralized, encrypted, offline-first messaging for the post-platform era.',
    url: 'https://krtr-mesh.vercel.app',
    siteName: 'KRTR Mesh',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'KRTR Mesh - Decentralized Messaging',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'KRTR Mesh - Decentralized Encrypted Messaging',
    description: 'Decentralized, encrypted, offline-first messaging for the post-platform era.',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${jetbrainsMono.variable}`}>
      <body className={`${inter.className} antialiased`}>
        {children}
      </body>
    </html>
  )
}
