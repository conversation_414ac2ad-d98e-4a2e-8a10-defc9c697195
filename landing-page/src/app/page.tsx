'use client'

import { motion } from 'framer-motion'
import HeroSec<PERSON> from '@/components/HeroSection'
import FeaturesSection from '@/components/FeaturesSection'
import TechSection from '@/components/TechSection'
import AppShowcase from '@/components/AppShowcase'
import DownloadSection from '@/components/DownloadSection'
import Footer from '@/components/Footer'

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900">
      <HeroSection />
      <FeaturesSection />
      <TechSection />
      <AppShowcase />
      <DownloadSection />
      <Footer />
    </main>
  )
}
