{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "ad696c7227fcbfd01776d6af415ae35f", "previewModeSigningKey": "3a99bf18668b7585463da0cd09ba337938f3bbc939a00016efd933a6641a0cf7", "previewModeEncryptionKey": "a8967058260330ef07dc3379ff7296f7964a8220d6e8878db36ac70944ac5de5"}}