(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2566:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,originalPathname:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>h}),i(5480),i(2029),i(5866);var r=i(3191),s=i(8716),n=i(7922),a=i.n(n),o=i(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let h=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,5480)),"/Users/<USER>/krtr-mesh-local/landing-page/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,2029)),"/Users/<USER>/krtr-mesh-local/landing-page/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,5866,23)),"next/dist/client/components/not-found-error"]}],u=["/Users/<USER>/krtr-mesh-local/landing-page/src/app/page.tsx"],c="/page",d={require:i,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},1968:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,2994,23)),Promise.resolve().then(i.t.bind(i,6114,23)),Promise.resolve().then(i.t.bind(i,9727,23)),Promise.resolve().then(i.t.bind(i,9671,23)),Promise.resolve().then(i.t.bind(i,1868,23)),Promise.resolve().then(i.t.bind(i,4759,23))},6581:()=>{},6563:(e,t,i)=>{Promise.resolve().then(i.bind(i,3263))},3263:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>np});var r=i(326),s=i(7577);let n=(0,s.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),a=(0,s.createContext)({}),o=(0,s.createContext)(null),l="undefined"!=typeof document,h=l?s.useLayoutEffect:s.useEffect,u=(0,s.createContext)({strict:!1}),c=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),d="data-"+c("framerAppearId");function p(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function m(e){return"string"==typeof e||Array.isArray(e)}function f(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}let y=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],g=["initial",...y];function x(e){return f(e.animate)||g.some(t=>m(e[t]))}function v(e){return!!(x(e)||e.variants)}function b(e){return Array.isArray(e)?e.join(" "):e}let w={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},j={};for(let e in w)j[e]={isEnabled:t=>w[e].some(e=>!!t[e])};let P=(0,s.createContext)({}),k=(0,s.createContext)({}),N=Symbol.for("motionComponentSymbol"),T=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function A(e){if("string"!=typeof e||e.includes("-"));else if(T.indexOf(e)>-1||/[A-Z]/.test(e))return!0;return!1}let S={},M=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],V=new Set(M);function E(e,{layout:t,layoutId:i}){return V.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!S[e]||"opacity"===e)}let C=e=>!!(e&&e.getVelocity),D={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},R=M.length,L=e=>t=>"string"==typeof t&&t.startsWith(e),B=L("--"),F=L("var(--"),I=(e,t)=>t&&"number"==typeof e?t.transform(e):e,O=(e,t,i)=>Math.min(Math.max(i,e),t),U={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},z={...U,transform:e=>O(0,1,e)},$={...U,default:1},W=e=>Math.round(1e5*e)/1e5,H=/(-)?([\d]*\.?[\d])+/g,G=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,K=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function q(e){return"string"==typeof e}let Z=e=>({test:t=>q(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),X=Z("deg"),Y=Z("%"),_=Z("px"),J=Z("vh"),Q=Z("vw"),ee={...Y,parse:e=>Y.parse(e)/100,transform:e=>Y.transform(100*e)},et={...U,transform:Math.round},ei={borderWidth:_,borderTopWidth:_,borderRightWidth:_,borderBottomWidth:_,borderLeftWidth:_,borderRadius:_,radius:_,borderTopLeftRadius:_,borderTopRightRadius:_,borderBottomRightRadius:_,borderBottomLeftRadius:_,width:_,maxWidth:_,height:_,maxHeight:_,size:_,top:_,right:_,bottom:_,left:_,padding:_,paddingTop:_,paddingRight:_,paddingBottom:_,paddingLeft:_,margin:_,marginTop:_,marginRight:_,marginBottom:_,marginLeft:_,rotate:X,rotateX:X,rotateY:X,rotateZ:X,scale:$,scaleX:$,scaleY:$,scaleZ:$,skew:X,skewX:X,skewY:X,distance:_,translateX:_,translateY:_,translateZ:_,x:_,y:_,z:_,perspective:_,transformPerspective:_,opacity:z,originX:ee,originY:ee,originZ:_,zIndex:et,fillOpacity:z,strokeOpacity:z,numOctaves:et};function er(e,t,i,r){let{style:s,vars:n,transform:a,transformOrigin:o}=e,l=!1,h=!1,u=!0;for(let e in t){let i=t[e];if(B(e)){n[e]=i;continue}let r=ei[e],c=I(i,r);if(V.has(e)){if(l=!0,a[e]=c,!u)continue;i!==(r.default||0)&&(u=!1)}else e.startsWith("origin")?(h=!0,o[e]=c):s[e]=c}if(!t.transform&&(l||r?s.transform=function(e,{enableHardwareAcceleration:t=!0,allowTransformNone:i=!0},r,s){let n="";for(let t=0;t<R;t++){let i=M[t];if(void 0!==e[i]){let t=D[i]||i;n+=`${t}(${e[i]}) `}}return t&&!e.z&&(n+="translateZ(0)"),n=n.trim(),s?n=s(e,r?"":n):i&&r&&(n="none"),n}(e.transform,i,u,r):s.transform&&(s.transform="none")),h){let{originX:e="50%",originY:t="50%",originZ:i=0}=o;s.transformOrigin=`${e} ${t} ${i}`}}let es=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function en(e,t,i){for(let r in t)C(t[r])||E(r,i)||(e[r]=t[r])}let ea=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eo(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||ea.has(e)}let el=e=>!eo(e);try{!function(e){e&&(el=t=>t.startsWith("on")?!eo(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}function eh(e,t,i){return"string"==typeof e?e:_.transform(t+i*e)}let eu={offset:"stroke-dashoffset",array:"stroke-dasharray"},ec={offset:"strokeDashoffset",array:"strokeDasharray"};function ed(e,{attrX:t,attrY:i,attrScale:r,originX:s,originY:n,pathLength:a,pathSpacing:o=1,pathOffset:l=0,...h},u,c,d){if(er(e,h,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:p,style:m,dimensions:f}=e;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==s||void 0!==n||m.transform)&&(m.transformOrigin=function(e,t,i){let r=eh(t,e.x,e.width),s=eh(i,e.y,e.height);return`${r} ${s}`}(f,void 0!==s?s:.5,void 0!==n?n:.5)),void 0!==t&&(p.x=t),void 0!==i&&(p.y=i),void 0!==r&&(p.scale=r),void 0!==a&&function(e,t,i=1,r=0,s=!0){e.pathLength=1;let n=s?eu:ec;e[n.offset]=_.transform(-r);let a=_.transform(t),o=_.transform(i);e[n.array]=`${a} ${o}`}(p,a,o,l,!1)}let ep=()=>({...es(),attrs:{}}),em=e=>"string"==typeof e&&"svg"===e.toLowerCase();function ef(e,{style:t,vars:i},r,s){for(let n in Object.assign(e.style,t,s&&s.getProjectionStyles(r)),i)e.style.setProperty(n,i[n])}let ey=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function eg(e,t,i,r){for(let i in ef(e,t,void 0,r),t.attrs)e.setAttribute(ey.has(i)?i:c(i),t.attrs[i])}function ex(e,t){let{style:i}=e,r={};for(let s in i)(C(i[s])||t.style&&C(t.style[s])||E(s,e))&&(r[s]=i[s]);return r}function ev(e,t){let i=ex(e,t);for(let r in e)(C(e[r])||C(t[r]))&&(i[-1!==M.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return i}function eb(e,t,i,r={},s={}){return"function"==typeof t&&(t=t(void 0!==i?i:e.custom,r,s)),"string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t&&(t=t(void 0!==i?i:e.custom,r,s)),t}let ew=e=>Array.isArray(e),ej=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),eP=e=>ew(e)?e[e.length-1]||0:e;function ek(e){let t=C(e)?e.get():e;return ej(t)?t.toValue():t}let eN=e=>(t,i)=>{let r=(0,s.useContext)(a),n=(0,s.useContext)(o),l=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:i},r,s,n){let a={latestValues:function(e,t,i,r){let s={},n=r(e,{});for(let e in n)s[e]=ek(n[e]);let{initial:a,animate:o}=e,l=x(e),h=v(e);t&&h&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===o&&(o=t.animate));let u=!!i&&!1===i.initial,c=(u=u||!1===a)?o:a;return c&&"boolean"!=typeof c&&!f(c)&&(Array.isArray(c)?c:[c]).forEach(t=>{let i=eb(e,t);if(!i)return;let{transitionEnd:r,transition:n,...a}=i;for(let e in a){let t=a[e];if(Array.isArray(t)){let e=u?t.length-1:0;t=t[e]}null!==t&&(s[e]=t)}for(let e in r)s[e]=r[e]}),s}(r,s,n,e),renderState:t()};return i&&(a.mount=e=>i(r,e,a)),a})(e,t,r,n);return i?l():function(e){let t=(0,s.useRef)(null);return null===t.current&&(t.current=e()),t.current}(l)},eT=e=>e;class eA{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){let t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}let eS=["prepare","read","update","preRender","render","postRender"],{schedule:eM,cancel:eV,state:eE,steps:eC}=function(e,t){let i=!1,r=!0,s={delta:0,timestamp:0,isProcessing:!1},n=eS.reduce((e,t)=>(e[t]=function(e){let t=new eA,i=new eA,r=0,s=!1,n=!1,a=new WeakSet,o={schedule:(e,n=!1,o=!1)=>{let l=o&&s,h=l?t:i;return n&&a.add(e),h.add(e)&&l&&s&&(r=t.order.length),e},cancel:e=>{i.remove(e),a.delete(e)},process:l=>{if(s){n=!0;return}if(s=!0,[t,i]=[i,t],i.clear(),r=t.order.length)for(let i=0;i<r;i++){let r=t.order[i];r(l),a.has(r)&&(o.schedule(r),e())}s=!1,n&&(n=!1,o.process(l))}};return o}(()=>i=!0),e),{}),a=e=>n[e].process(s),o=()=>{let n=performance.now();i=!1,s.delta=r?1e3/60:Math.max(Math.min(n-s.timestamp,40),1),s.timestamp=n,s.isProcessing=!0,eS.forEach(a),s.isProcessing=!1,i&&t&&(r=!1,e(o))},l=()=>{i=!0,r=!0,s.isProcessing||e(o)};return{schedule:eS.reduce((e,t)=>{let r=n[t];return e[t]=(e,t=!1,s=!1)=>(i||l(),r.schedule(e,t,s)),e},{}),cancel:e=>eS.forEach(t=>n[t].cancel(e)),state:s,steps:n}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:eT,!0),eD={useVisualState:eN({scrapeMotionValuesFromProps:ev,createRenderState:ep,onMount:(e,t,{renderState:i,latestValues:r})=>{eM.read(()=>{try{i.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){i.dimensions={x:0,y:0,width:0,height:0}}}),eM.render(()=>{ed(i,r,{enableHardwareAcceleration:!1},em(t.tagName),e.transformTemplate),eg(t,i)})}})},eR={useVisualState:eN({scrapeMotionValuesFromProps:ex,createRenderState:es})};function eL(e,t,i,r={passive:!0}){return e.addEventListener(t,i,r),()=>e.removeEventListener(t,i)}let eB=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eF(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}let eI=e=>t=>eB(t)&&e(t,eF(t));function eO(e,t,i,r){return eL(e,t,eI(i),r)}let eU=(e,t)=>i=>t(e(i)),ez=(...e)=>e.reduce(eU);function e$(e){let t=null;return()=>null===t&&(t=e,()=>{t=null})}let eW=e$("dragHorizontal"),eH=e$("dragVertical");function eG(e){let t=!1;if("y"===e)t=eH();else if("x"===e)t=eW();else{let e=eW(),i=eH();e&&i?t=()=>{e(),i()}:(e&&e(),i&&i())}return t}function eK(){let e=eG(!0);return!e||(e(),!1)}class eq{constructor(e){this.isMounted=!1,this.node=e}update(){}}function eZ(e,t){let i="onHover"+(t?"Start":"End");return eO(e.current,"pointer"+(t?"enter":"leave"),(r,s)=>{if("touch"===r.pointerType||eK())return;let n=e.getProps();e.animationState&&n.whileHover&&e.animationState.setActive("whileHover",t),n[i]&&eM.update(()=>n[i](r,s))},{passive:!e.getProps()[i]})}class eX extends eq{mount(){this.unmount=ez(eZ(this.node,!0),eZ(this.node,!1))}unmount(){}}class eY extends eq{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ez(eL(this.node.current,"focus",()=>this.onFocus()),eL(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let e_=(e,t)=>!!t&&(e===t||e_(e,t.parentElement));function eJ(e,t){if(!t)return;let i=new PointerEvent("pointer"+e);t(i,eF(i))}class eQ extends eq{constructor(){super(...arguments),this.removeStartListeners=eT,this.removeEndListeners=eT,this.removeAccessibleListeners=eT,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();let i=this.node.getProps(),r=eO(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;let{onTap:i,onTapCancel:r,globalTapTarget:s}=this.node.getProps();eM.update(()=>{s||e_(this.node.current,e.target)?i&&i(e,t):r&&r(e,t)})},{passive:!(i.onTap||i.onPointerUp)}),s=eO(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(i.onTapCancel||i.onPointerCancel)});this.removeEndListeners=ez(r,s),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=eL(this.node.current,"keydown",e=>{"Enter"!==e.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=eL(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&eJ("up",(e,t)=>{let{onTap:i}=this.node.getProps();i&&eM.update(()=>i(e,t))})}),eJ("down",(e,t)=>{this.startPress(e,t)}))}),t=eL(this.node.current,"blur",()=>{this.isPressing&&eJ("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=ez(e,t)}}startPress(e,t){this.isPressing=!0;let{onTapStart:i,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),i&&eM.update(()=>i(e,t))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!eK()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:i}=this.node.getProps();i&&eM.update(()=>i(e,t))}mount(){let e=this.node.getProps(),t=eO(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),i=eL(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=ez(t,i)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let e0=new WeakMap,e1=new WeakMap,e2=e=>{let t=e0.get(e.target);t&&t(e)},e5=e=>{e.forEach(e2)},e3={some:0,all:1};class e4 extends eq{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:r="some",once:s}=e,n={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:e3[r]};return function(e,t,i){let r=function({root:e,...t}){let i=e||document;e1.has(i)||e1.set(i,{});let r=e1.get(i),s=JSON.stringify(t);return r[s]||(r[s]=new IntersectionObserver(e5,{root:e,...t})),r[s]}(t);return e0.set(e,i),r.observe(e),()=>{e0.delete(e),r.unobserve(e)}}(this.node.current,n,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,s&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),n=t?i:r;n&&n(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}function e6(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let r=0;r<i;r++)if(t[r]!==e[r])return!1;return!0}function e8(e,t,i){let r=e.getProps();return eb(r,t,void 0!==i?i:r.custom,function(e){let t={};return e.values.forEach((e,i)=>t[i]=e.get()),t}(e),function(e){let t={};return e.values.forEach((e,i)=>t[i]=e.getVelocity()),t}(e))}let e9=e=>1e3*e,e7=e=>e/1e3,te={current:!1},tt=e=>Array.isArray(e)&&"number"==typeof e[0],ti=([e,t,i,r])=>`cubic-bezier(${e}, ${t}, ${i}, ${r})`,tr={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ti([0,.65,.55,1]),circOut:ti([.55,0,1,.45]),backIn:ti([.31,.01,.66,-.59]),backOut:ti([.33,1.53,.69,.99])},ts=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function tn(e,t,i,r){if(e===t&&i===r)return eT;let s=t=>(function(e,t,i,r,s){let n,a;let o=0;do(n=ts(a=t+(i-t)/2,r,s)-e)>0?i=a:t=a;while(Math.abs(n)>1e-7&&++o<12);return a})(t,0,1,e,i);return e=>0===e||1===e?e:ts(s(e),t,r)}let ta=tn(.42,0,1,1),to=tn(0,0,.58,1),tl=tn(.42,0,.58,1),th=e=>Array.isArray(e)&&"number"!=typeof e[0],tu=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,tc=e=>t=>1-e(1-t),td=e=>1-Math.sin(Math.acos(e)),tp=tc(td),tm=tu(td),tf=tn(.33,1.53,.69,.99),ty=tc(tf),tg=tu(ty),tx={linear:eT,easeIn:ta,easeInOut:tl,easeOut:to,circIn:td,circInOut:tm,circOut:tp,backIn:ty,backInOut:tg,backOut:tf,anticipate:e=>(e*=2)<1?.5*ty(e):.5*(2-Math.pow(2,-10*(e-1)))},tv=e=>{if(Array.isArray(e)){eT(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,r,s]=e;return tn(t,i,r,s)}return"string"==typeof e?(eT(void 0!==tx[e],`Invalid easing type '${e}'`),tx[e]):e},tb=(e,t)=>i=>!!(q(i)&&K.test(i)&&i.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(i,t)),tw=(e,t,i)=>r=>{if(!q(r))return r;let[s,n,a,o]=r.match(H);return{[e]:parseFloat(s),[t]:parseFloat(n),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tj=e=>O(0,255,e),tP={...U,transform:e=>Math.round(tj(e))},tk={test:tb("rgb","red"),parse:tw("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:r=1})=>"rgba("+tP.transform(e)+", "+tP.transform(t)+", "+tP.transform(i)+", "+W(z.transform(r))+")"},tN={test:tb("#"),parse:function(e){let t="",i="",r="",s="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,i+=i,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}},transform:tk.transform},tT={test:tb("hsl","hue"),parse:tw("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:r=1})=>"hsla("+Math.round(e)+", "+Y.transform(W(t))+", "+Y.transform(W(i))+", "+W(z.transform(r))+")"},tA={test:e=>tk.test(e)||tN.test(e)||tT.test(e),parse:e=>tk.test(e)?tk.parse(e):tT.test(e)?tT.parse(e):tN.parse(e),transform:e=>q(e)?e:e.hasOwnProperty("red")?tk.transform(e):tT.transform(e)},tS=(e,t,i)=>-i*e+i*t+e;function tM(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}let tV=(e,t,i)=>{let r=e*e;return Math.sqrt(Math.max(0,i*(t*t-r)+r))},tE=[tN,tk,tT],tC=e=>tE.find(t=>t.test(e));function tD(e){let t=tC(e);eT(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`);let i=t.parse(e);return t===tT&&(i=function({hue:e,saturation:t,lightness:i,alpha:r}){e/=360,i/=100;let s=0,n=0,a=0;if(t/=100){let r=i<.5?i*(1+t):i+t-i*t,o=2*i-r;s=tM(o,r,e+1/3),n=tM(o,r,e),a=tM(o,r,e-1/3)}else s=n=a=i;return{red:Math.round(255*s),green:Math.round(255*n),blue:Math.round(255*a),alpha:r}}(i)),i}let tR=(e,t)=>{let i=tD(e),r=tD(t),s={...i};return e=>(s.red=tV(i.red,r.red,e),s.green=tV(i.green,r.green,e),s.blue=tV(i.blue,r.blue,e),s.alpha=tS(i.alpha,r.alpha,e),tk.transform(s))},tL={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:eT},tB={regex:G,countKey:"Colors",token:"${c}",parse:tA.parse},tF={regex:H,countKey:"Numbers",token:"${n}",parse:U.parse};function tI(e,{regex:t,countKey:i,token:r,parse:s}){let n=e.tokenised.match(t);n&&(e["num"+i]=n.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...n.map(s)))}function tO(e){let t=e.toString(),i={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return i.value.includes("var(--")&&tI(i,tL),tI(i,tB),tI(i,tF),i}function tU(e){return tO(e).values}function tz(e){let{values:t,numColors:i,numVars:r,tokenised:s}=tO(e),n=t.length;return e=>{let t=s;for(let s=0;s<n;s++)t=s<r?t.replace(tL.token,e[s]):s<r+i?t.replace(tB.token,tA.transform(e[s])):t.replace(tF.token,W(e[s]));return t}}let t$=e=>"number"==typeof e?0:e,tW={test:function(e){var t,i;return isNaN(e)&&q(e)&&((null===(t=e.match(H))||void 0===t?void 0:t.length)||0)+((null===(i=e.match(G))||void 0===i?void 0:i.length)||0)>0},parse:tU,createTransformer:tz,getAnimatableNone:function(e){let t=tU(e);return tz(e)(t.map(t$))}},tH=(e,t)=>i=>`${i>0?t:e}`;function tG(e,t){return"number"==typeof e?i=>tS(e,t,i):tA.test(e)?tR(e,t):e.startsWith("var(")?tH(e,t):tZ(e,t)}let tK=(e,t)=>{let i=[...e],r=i.length,s=e.map((e,i)=>tG(e,t[i]));return e=>{for(let t=0;t<r;t++)i[t]=s[t](e);return i}},tq=(e,t)=>{let i={...e,...t},r={};for(let s in i)void 0!==e[s]&&void 0!==t[s]&&(r[s]=tG(e[s],t[s]));return e=>{for(let t in r)i[t]=r[t](e);return i}},tZ=(e,t)=>{let i=tW.createTransformer(t),r=tO(e),s=tO(t);return r.numVars===s.numVars&&r.numColors===s.numColors&&r.numNumbers>=s.numNumbers?ez(tK(r.values,s.values),i):(eT(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tH(e,t))},tX=(e,t,i)=>{let r=t-e;return 0===r?1:(i-e)/r},tY=(e,t)=>i=>tS(e,t,i);function t_(e,t,{clamp:i=!0,ease:r,mixer:s}={}){let n=e.length;if(eT(n===t.length,"Both input and output ranges must be the same length"),1===n)return()=>t[0];e[0]>e[n-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,i){let r=[],s=i||function(e){if("number"==typeof e);else if("string"==typeof e)return tA.test(e)?tR:tZ;else if(Array.isArray(e))return tK;else if("object"==typeof e)return tq;return tY}(e[0]),n=e.length-1;for(let i=0;i<n;i++){let n=s(e[i],e[i+1]);t&&(n=ez(Array.isArray(t)?t[i]||eT:t,n)),r.push(n)}return r}(t,r,s),o=a.length,l=t=>{let i=0;if(o>1)for(;i<e.length-2&&!(t<e[i+1]);i++);let r=tX(e[i],e[i+1],t);return a[i](r)};return i?t=>l(O(e[0],e[n-1],t)):l}function tJ({duration:e=300,keyframes:t,times:i,ease:r="easeInOut"}){let s=th(r)?r.map(tv):tv(r),n={done:!1,value:t[0]},a=t_((i&&i.length===t.length?i:function(e){let t=[0];return function(e,t){let i=e[e.length-1];for(let r=1;r<=t;r++){let s=tX(0,t,r);e.push(tS(i,1,s))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(s)?s:t.map(()=>s||tl).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(n.value=a(t),n.done=t>=e,n)}}function tQ(e,t,i){var r,s;let n=Math.max(t-5,0);return r=i-e(n),(s=t-n)?1e3/s*r:0}function t0(e,t){return e*Math.sqrt(1-t*t)}let t1=["duration","bounce"],t2=["stiffness","damping","mass"];function t5(e,t){return t.some(t=>void 0!==e[t])}function t3({keyframes:e,restDelta:t,restSpeed:i,...r}){let s;let n=e[0],a=e[e.length-1],o={done:!1,value:n},{stiffness:l,damping:h,mass:u,duration:c,velocity:d,isResolvedFromDuration:p}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!t5(e,t2)&&t5(e,t1)){let i=function({duration:e=800,bounce:t=.25,velocity:i=0,mass:r=1}){let s,n;eT(e<=e9(10),"Spring duration must be 10 seconds or less");let a=1-t;a=O(.05,1,a),e=O(.01,10,e7(e)),a<1?(s=t=>{let r=t*a,s=r*e;return .001-(r-i)/t0(t,a)*Math.exp(-s)},n=t=>{let r=t*a*e,n=Math.pow(a,2)*Math.pow(t,2)*e,o=t0(Math.pow(t,2),a);return(r*i+i-n)*Math.exp(-r)*(-s(t)+.001>0?-1:1)/o}):(s=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),n=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let r=i;for(let i=1;i<12;i++)r-=e(r)/t(r);return r}(s,n,5/e);if(e=e9(e),isNaN(o))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(o,2)*r;return{stiffness:t,damping:2*a*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...i,mass:1}).isResolvedFromDuration=!0}return t}({...r,velocity:-e7(r.velocity||0)}),m=d||0,f=h/(2*Math.sqrt(l*u)),y=a-n,g=e7(Math.sqrt(l/u)),x=5>Math.abs(y);if(i||(i=x?.01:2),t||(t=x?.005:.5),f<1){let e=t0(g,f);s=t=>a-Math.exp(-f*g*t)*((m+f*g*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===f)s=e=>a-Math.exp(-g*e)*(y+(m+g*y)*e);else{let e=g*Math.sqrt(f*f-1);s=t=>{let i=Math.exp(-f*g*t),r=Math.min(e*t,300);return a-i*((m+f*g*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}return{calculatedDuration:p&&c||null,next:e=>{let r=s(e);if(p)o.done=e>=c;else{let n=m;0!==e&&(n=f<1?tQ(s,e,r):0);let l=Math.abs(n)<=i,h=Math.abs(a-r)<=t;o.done=l&&h}return o.value=o.done?a:r,o}}}function t4({keyframes:e,velocity:t=0,power:i=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:n=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:u}){let c,d;let p=e[0],m={done:!1,value:p},f=e=>void 0!==o&&e<o||void 0!==l&&e>l,y=e=>void 0===o?l:void 0===l?o:Math.abs(o-e)<Math.abs(l-e)?o:l,g=i*t,x=p+g,v=void 0===a?x:a(x);v!==x&&(g=v-p);let b=e=>-g*Math.exp(-e/r),w=e=>v+b(e),j=e=>{let t=b(e),i=w(e);m.done=Math.abs(t)<=h,m.value=m.done?v:i},P=e=>{f(m.value)&&(c=e,d=t3({keyframes:[m.value,y(m.value)],velocity:tQ(w,e,m.value),damping:s,stiffness:n,restDelta:h,restSpeed:u}))};return P(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==c||(t=!0,j(e),P(e)),void 0!==c&&e>c)?d.next(e-c):(t||j(e),m)}}}let t6=e=>{let t=({timestamp:t})=>e(t);return{start:()=>eM.update(t,!0),stop:()=>eV(t),now:()=>eE.isProcessing?eE.timestamp:performance.now()}};function t8(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}let t9={decay:t4,inertia:t4,tween:tJ,keyframes:tJ,spring:t3};function t7({autoplay:e=!0,delay:t=0,driver:i=t6,keyframes:r,type:s="keyframes",repeat:n=0,repeatDelay:a=0,repeatType:o="loop",onPlay:l,onStop:h,onComplete:u,onUpdate:c,...d}){let p,m,f,y,g,x=1,v=!1,b=()=>{m=new Promise(e=>{p=e})};b();let w=t9[s]||tJ;w!==tJ&&"number"!=typeof r[0]&&(y=t_([0,100],r,{clamp:!1}),r=[0,100]);let j=w({...d,keyframes:r});"mirror"===o&&(g=w({...d,keyframes:[...r].reverse(),velocity:-(d.velocity||0)}));let P="idle",k=null,N=null,T=null;null===j.calculatedDuration&&n&&(j.calculatedDuration=t8(j));let{calculatedDuration:A}=j,S=1/0,M=1/0;null!==A&&(M=(S=A+a)*(n+1)-a);let V=0,E=e=>{if(null===N)return;x>0&&(N=Math.min(N,e)),x<0&&(N=Math.min(e-M/x,N));let i=(V=null!==k?k:Math.round(e-N)*x)-t*(x>=0?1:-1),s=x>=0?i<0:i>M;V=Math.max(i,0),"finished"===P&&null===k&&(V=M);let l=V,h=j;if(n){let e=Math.min(V,M)/S,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,n+1))%2&&("reverse"===o?(i=1-i,a&&(i-=a/S)):"mirror"===o&&(h=g)),l=O(0,1,i)*S}let u=s?{done:!1,value:r[0]}:h.next(l);y&&(u.value=y(u.value));let{done:d}=u;s||null===A||(d=x>=0?V>=M:V<=0);let p=null===k&&("finished"===P||"running"===P&&d);return c&&c(u.value),p&&R(),u},C=()=>{f&&f.stop(),f=void 0},D=()=>{P="idle",C(),p(),b(),N=T=null},R=()=>{P="finished",u&&u(),C(),p()},L=()=>{if(v)return;f||(f=i(E));let e=f.now();l&&l(),null!==k?N=e-k:N&&"finished"!==P||(N=e),"finished"===P&&b(),T=N,k=null,P="running",f.start()};e&&L();let B={then:(e,t)=>m.then(e,t),get time(){return e7(V)},set time(newTime){V=newTime=e9(newTime),null===k&&f&&0!==x?N=f.now()-newTime/x:k=newTime},get duration(){return e7(null===j.calculatedDuration?t8(j):j.calculatedDuration)},get speed(){return x},set speed(newSpeed){if(newSpeed===x||!f)return;x=newSpeed,B.time=e7(V)},get state(){return P},play:L,pause:()=>{P="paused",k=V},stop:()=>{v=!0,"idle"!==P&&(P="idle",h&&h(),D())},cancel:()=>{null!==T&&E(T),D()},complete:()=>{P="finished"},sample:e=>(N=0,E(e))};return B}let ie=function(e){let t;return()=>(void 0===t&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),it=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),ii=(e,t)=>"spring"===t.type||"backgroundColor"===e||!function e(t){return!!(!t||"string"==typeof t&&tr[t]||tt(t)||Array.isArray(t)&&t.every(e))}(t.ease),ir={type:"spring",stiffness:500,damping:25,restSpeed:10},is=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),ia={type:"keyframes",duration:.8},io={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},il=(e,{keyframes:t})=>t.length>2?ia:V.has(e)?e.startsWith("scale")?is(t[1]):ir:io,ih=(e,t)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tW.test(t)||"0"===t)&&!t.startsWith("url(")),iu=new Set(["brightness","contrast","saturate","opacity"]);function ic(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=i.match(H)||[];if(!r)return e;let s=i.replace(r,""),n=iu.has(t)?1:0;return r!==i&&(n*=100),t+"("+n+s+")"}let id=/([a-z-]*)\(.*?\)/g,ip={...tW,getAnimatableNone:e=>{let t=e.match(id);return t?t.map(ic).join(" "):e}},im={...ei,color:tA,backgroundColor:tA,outlineColor:tA,fill:tA,stroke:tA,borderColor:tA,borderTopColor:tA,borderRightColor:tA,borderBottomColor:tA,borderLeftColor:tA,filter:ip,WebkitFilter:ip},iy=e=>im[e];function ig(e,t){let i=iy(e);return i!==ip&&(i=tW),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let ix=e=>/^0[^.\s]+$/.test(e);function iv(e,t){return e[t]||e.default||e}let ib={skipAnimations:!1},iw=(e,t,i,r={})=>s=>{let n=iv(r,e)||{},a=n.delay||r.delay||0,{elapsed:o=0}=r;o-=e9(a);let l=function(e,t,i,r){let s,n;let a=ih(t,i);s=Array.isArray(i)?[...i]:[null,i];let o=void 0!==r.from?r.from:e.get(),l=[];for(let e=0;e<s.length;e++){var h;null===s[e]&&(s[e]=0===e?o:s[e-1]),("number"==typeof(h=s[e])?0===h:null!==h?"none"===h||"0"===h||ix(h):void 0)&&l.push(e),"string"==typeof s[e]&&"none"!==s[e]&&"0"!==s[e]&&(n=s[e])}if(a&&l.length&&n)for(let e=0;e<l.length;e++)s[l[e]]=ig(t,n);return s}(t,e,i,n),h=l[0],u=l[l.length-1],c=ih(e,h),d=ih(e,u);eT(c===d,`You are trying to animate ${e} from "${h}" to "${u}". ${h} is not an animatable value - to enable this animation set ${h} to a value animatable to ${u} via the \`style\` property.`);let p={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...n,delay:-o,onUpdate:e=>{t.set(e),n.onUpdate&&n.onUpdate(e)},onComplete:()=>{s(),n.onComplete&&n.onComplete()}};if(!function({when:e,delay:t,delayChildren:i,staggerChildren:r,staggerDirection:s,repeat:n,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(n)&&(p={...p,...il(e,p)}),p.duration&&(p.duration=e9(p.duration)),p.repeatDelay&&(p.repeatDelay=e9(p.repeatDelay)),!c||!d||te.current||!1===n.type||ib.skipAnimations)return function({keyframes:e,delay:t,onUpdate:i,onComplete:r}){let s=()=>(i&&i(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:eT,pause:eT,stop:eT,then:e=>(e(),Promise.resolve()),cancel:eT,complete:eT});return t?t7({keyframes:[0,1],duration:0,delay:t,onComplete:s}):s()}(te.current?{...p,delay:0}:p);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let i=function(e,t,{onUpdate:i,onComplete:r,...s}){let n,a;if(!(ie()&&it.has(t)&&!s.repeatDelay&&"mirror"!==s.repeatType&&0!==s.damping&&"inertia"!==s.type))return!1;let o=!1,l=!1,h=()=>{a=new Promise(e=>{n=e})};h();let{keyframes:u,duration:c=300,ease:d,times:p}=s;if(ii(t,s)){let e=t7({...s,repeat:0,delay:0}),t={done:!1,value:u[0]},i=[],r=0;for(;!t.done&&r<2e4;)t=e.sample(r),i.push(t.value),r+=10;p=void 0,u=i,c=r-10,d="linear"}let m=function(e,t,i,{delay:r=0,duration:s,repeat:n=0,repeatType:a="loop",ease:o,times:l}={}){let h={[t]:i};l&&(h.offset=l);let u=function e(t){if(t)return tt(t)?ti(t):Array.isArray(t)?t.map(e):tr[t]}(o);return Array.isArray(u)&&(h.easing=u),e.animate(h,{delay:r,duration:s,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:n+1,direction:"reverse"===a?"alternate":"normal"})}(e.owner.current,t,u,{...s,duration:c,ease:d,times:p}),f=()=>{l=!1,m.cancel()},y=()=>{l=!0,eM.update(f),n(),h()};return m.onfinish=()=>{l||(e.set(function(e,{repeat:t,repeatType:i="loop"}){let r=t&&"loop"!==i&&t%2==1?0:e.length-1;return e[r]}(u,s)),r&&r(),y())},{then:(e,t)=>a.then(e,t),attachTimeline:e=>(m.timeline=e,m.onfinish=null,eT),get time(){return e7(m.currentTime||0)},set time(newTime){m.currentTime=e9(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return e7(c)},play:()=>{o||(m.play(),eV(f))},pause:()=>m.pause(),stop:()=>{if(o=!0,"idle"===m.playState)return;let{currentTime:t}=m;if(t){let i=t7({...s,autoplay:!1});e.setWithVelocity(i.sample(t-10).value,i.sample(t).value,10)}y()},complete:()=>{l||m.finish()},cancel:y}}(t,e,p);if(i)return i}return t7(p)};function ij(e){return!!(C(e)&&e.add)}let iP=e=>/^\-?\d*\.?\d+$/.test(e);function ik(e,t){-1===e.indexOf(t)&&e.push(t)}function iN(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class iT{constructor(){this.subscriptions=[]}add(e){return ik(this.subscriptions,e),()=>iN(this.subscriptions,e)}notify(e,t,i){let r=this.subscriptions.length;if(r){if(1===r)this.subscriptions[0](e,t,i);else for(let s=0;s<r;s++){let r=this.subscriptions[s];r&&r(e,t,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let iA=e=>!isNaN(parseFloat(e)),iS={current:void 0};class iM{constructor(e,t={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(e,t=!0)=>{this.prev=this.current,this.current=e;let{delta:i,timestamp:r}=eE;this.lastUpdated!==r&&(this.timeDelta=i,this.lastUpdated=r,eM.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>eM.postRender(this.velocityCheck),this.velocityCheck=({timestamp:e})=>{e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=iA(this.current),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new iT);let i=this.events[e].add(t);return"change"===e?()=>{i(),eM.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=e,this.timeDelta=i}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return iS.current&&iS.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e,t;return this.canTrackVelocity?(e=parseFloat(this.current)-parseFloat(this.prev),(t=this.timeDelta)?1e3/t*e:0):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function iV(e,t){return new iM(e,t)}let iE=e=>t=>t.test(e),iC=[U,_,Y,X,Q,J,{test:e=>"auto"===e,parse:e=>e}],iD=e=>iC.find(iE(e)),iR=[...iC,tA,tW],iL=e=>iR.find(iE(e));function iB(e,t,{delay:i=0,transitionOverride:r,type:s}={}){let{transition:n=e.getDefaultTransition(),transitionEnd:a,...o}=e.makeTargetAnimatable(t),l=e.getValue("willChange");r&&(n=r);let h=[],u=s&&e.animationState&&e.animationState.getState()[s];for(let t in o){let r=e.getValue(t),s=o[t];if(!r||void 0===s||u&&function({protectedKeys:e,needsAnimating:t},i){let r=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,r}(u,t))continue;let a={delay:i,elapsed:0,...iv(n||{},t)};if(window.HandoffAppearAnimations){let i=e.getProps()[d];if(i){let e=window.HandoffAppearAnimations(i,t,r,eM);null!==e&&(a.elapsed=e,a.isHandoff=!0)}}let c=!a.isHandoff&&!function(e,t){let i=e.get();if(!Array.isArray(t))return i!==t;for(let e=0;e<t.length;e++)if(t[e]!==i)return!0}(r,s);if("spring"===a.type&&(r.getVelocity()||a.velocity)&&(c=!1),r.animation&&(c=!1),c)continue;r.start(iw(t,r,s,e.shouldReduceMotion&&V.has(t)?{type:!1}:a));let p=r.animation;ij(l)&&(l.add(t),p.then(()=>l.remove(t))),h.push(p)}return a&&Promise.all(h).then(()=>{a&&function(e,t){let i=e8(e,t),{transitionEnd:r={},transition:s={},...n}=i?e.makeTargetAnimatable(i,!1):{};for(let t in n={...n,...r}){let i=eP(n[t]);e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,iV(i))}}(e,a)}),h}function iF(e,t,i={}){let r=e8(e,t,i.custom),{transition:s=e.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(s=i.transitionOverride);let n=r?()=>Promise.all(iB(e,r,i)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:n=0,staggerChildren:a,staggerDirection:o}=s;return function(e,t,i=0,r=0,s=1,n){let a=[],o=(e.variantChildren.size-1)*r,l=1===s?(e=0)=>e*r:(e=0)=>o-e*r;return Array.from(e.variantChildren).sort(iI).forEach((e,r)=>{e.notify("AnimationStart",t),a.push(iF(e,t,{...n,delay:i+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,n+r,a,o,i)}:()=>Promise.resolve(),{when:o}=s;if(!o)return Promise.all([n(),a(i.delay)]);{let[e,t]="beforeChildren"===o?[n,a]:[a,n];return e().then(()=>t())}}function iI(e,t){return e.sortNodePosition(t)}let iO=[...y].reverse(),iU=y.length;function iz(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class i$ extends eq{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>iF(e,t,i)));else if("string"==typeof t)r=iF(e,t,i);else{let s="function"==typeof t?e8(e,t,i.custom):t;r=Promise.all(iB(e,s,i))}return r.then(()=>e.notify("AnimationComplete",t))})(e,t,i))),i={animate:iz(!0),whileInView:iz(),whileHover:iz(),whileTap:iz(),whileDrag:iz(),whileFocus:iz(),exit:iz()},r=!0,s=(t,i)=>{let r=e8(e,i);if(r){let{transition:e,transitionEnd:i,...s}=r;t={...t,...s,...i}}return t};function n(n,a){let o=e.getProps(),l=e.getVariantContext(!0)||{},h=[],u=new Set,c={},d=1/0;for(let t=0;t<iU;t++){var p;let y=iO[t],g=i[y],x=void 0!==o[y]?o[y]:l[y],v=m(x),b=y===a?g.isActive:null;!1===b&&(d=t);let w=x===l[y]&&x!==o[y]&&v;if(w&&r&&e.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...c},!g.isActive&&null===b||!x&&!g.prevProp||f(x)||"boolean"==typeof x)continue;let j=(p=g.prevProp,("string"==typeof x?x!==p:!!Array.isArray(x)&&!e6(x,p))||y===a&&g.isActive&&!w&&v||t>d&&v),P=!1,k=Array.isArray(x)?x:[x],N=k.reduce(s,{});!1===b&&(N={});let{prevResolvedValues:T={}}=g,A={...T,...N},S=e=>{j=!0,u.has(e)&&(P=!0,u.delete(e)),g.needsAnimating[e]=!0};for(let e in A){let t=N[e],i=T[e];if(!c.hasOwnProperty(e))(ew(t)&&ew(i)?e6(t,i):t===i)?void 0!==t&&u.has(e)?S(e):g.protectedKeys[e]=!0:void 0!==t?S(e):u.add(e)}g.prevProp=x,g.prevResolvedValues=N,g.isActive&&(c={...c,...N}),r&&e.blockInitialAnimation&&(j=!1),j&&(!w||P)&&h.push(...k.map(e=>({animation:e,options:{type:y,...n}})))}if(u.size){let t={};u.forEach(i=>{let r=e.getBaseTarget(i);void 0!==r&&(t[i]=r)}),h.push({animation:t})}let y=!!h.length;return r&&(!1===o.initial||o.initial===o.animate)&&!e.manuallyAnimateOnMount&&(y=!1),r=!1,y?t(h):Promise.resolve()}return{animateChanges:n,setActive:function(t,r,s){var a;if(i[t].isActive===r)return Promise.resolve();null===(a=e.variantChildren)||void 0===a||a.forEach(e=>{var i;return null===(i=e.animationState)||void 0===i?void 0:i.setActive(t,r)}),i[t].isActive=r;let o=n(s,t);for(let e in i)i[e].protectedKeys={};return o},setAnimateFunction:function(i){t=i(e)},getState:()=>i}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),f(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}let iW=0;class iH extends eq{constructor(){super(...arguments),this.id=iW++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:i}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let s=this.node.animationState.setActive("exit",!e,{custom:null!=i?i:this.node.getProps().custom});t&&!e&&s.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}let iG=(e,t)=>Math.abs(e-t);class iK{constructor(e,t,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=iX(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(iG(e.x,t.x)**2+iG(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!i)return;let{point:r}=e,{timestamp:s}=eE;this.history.push({...r,timestamp:s});let{onStart:n,onMove:a}=this.handlers;t||(n&&n(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=iq(t,this.transformPagePoint),eM.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=iX("pointercancel"===e.type?this.lastMoveEventInfo:iq(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,n),r&&r(e,n)},!eB(e))return;this.dragSnapToOrigin=s,this.handlers=t,this.transformPagePoint=i,this.contextWindow=r||window;let n=iq(eF(e),this.transformPagePoint),{point:a}=n,{timestamp:o}=eE;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,iX(n,this.history)),this.removeListeners=ez(eO(this.contextWindow,"pointermove",this.handlePointerMove),eO(this.contextWindow,"pointerup",this.handlePointerUp),eO(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),eV(this.updatePoint)}}function iq(e,t){return t?{point:t(e.point)}:e}function iZ(e,t){return{x:e.x-t.x,y:e.y-t.y}}function iX({point:e},t){return{point:e,delta:iZ(e,iY(t)),offset:iZ(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,r=null,s=iY(e);for(;i>=0&&(r=e[i],!(s.timestamp-r.timestamp>e9(.1)));)i--;if(!r)return{x:0,y:0};let n=e7(s.timestamp-r.timestamp);if(0===n)return{x:0,y:0};let a={x:(s.x-r.x)/n,y:(s.y-r.y)/n};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,0)}}function iY(e){return e[e.length-1]}function i_(e){return e.max-e.min}function iJ(e,t=0,i=.01){return Math.abs(e-t)<=i}function iQ(e,t,i,r=.5){e.origin=r,e.originPoint=tS(t.min,t.max,e.origin),e.scale=i_(i)/i_(t),(iJ(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=tS(i.min,i.max,e.origin)-e.originPoint,(iJ(e.translate)||isNaN(e.translate))&&(e.translate=0)}function i0(e,t,i,r){iQ(e.x,t.x,i.x,r?r.originX:void 0),iQ(e.y,t.y,i.y,r?r.originY:void 0)}function i1(e,t,i){e.min=i.min+t.min,e.max=e.min+i_(t)}function i2(e,t,i){e.min=t.min-i.min,e.max=e.min+i_(t)}function i5(e,t,i){i2(e.x,t.x,i.x),i2(e.y,t.y,i.y)}function i3(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function i4(e,t){let i=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,r]=[r,i]),{min:i,max:r}}function i6(e,t,i){return{min:i8(e,t),max:i8(e,i)}}function i8(e,t){return"number"==typeof e?e:e[t]||0}let i9=()=>({translate:0,scale:1,origin:0,originPoint:0}),i7=()=>({x:i9(),y:i9()}),re=()=>({min:0,max:0}),rt=()=>({x:re(),y:re()});function ri(e){return[e("x"),e("y")]}function rr({top:e,left:t,right:i,bottom:r}){return{x:{min:t,max:i},y:{min:e,max:r}}}function rs(e){return void 0===e||1===e}function rn({scale:e,scaleX:t,scaleY:i}){return!rs(e)||!rs(t)||!rs(i)}function ra(e){return rn(e)||ro(e)||e.z||e.rotate||e.rotateX||e.rotateY}function ro(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function rl(e,t,i,r,s){return void 0!==s&&(e=r+s*(e-r)),r+i*(e-r)+t}function rh(e,t=0,i=1,r,s){e.min=rl(e.min,t,i,r,s),e.max=rl(e.max,t,i,r,s)}function ru(e,{x:t,y:i}){rh(e.x,t.translate,t.scale,t.originPoint),rh(e.y,i.translate,i.scale,i.originPoint)}function rc(e){return Number.isInteger(e)?e:e>1.0000000000001||e<.999999999999?e:1}function rd(e,t){e.min=e.min+t,e.max=e.max+t}function rp(e,t,[i,r,s]){let n=void 0!==t[s]?t[s]:.5,a=tS(e.min,e.max,n);rh(e,t[i],t[r],a,t.scale)}let rm=["x","scaleX","originX"],rf=["y","scaleY","originY"];function ry(e,t){rp(e.x,t,rm),rp(e.y,t,rf)}function rg(e,t){return rr(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let rx=({current:e})=>e?e.ownerDocument.defaultView:null,rv=new WeakMap;class rb{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rt(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iK(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(eF(e,"page").point)},onStart:(e,t)=>{let{drag:i,dragPropagation:r,onDragStart:s}=this.getProps();if(i&&!r&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=eG(i),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ri(e=>{let t=this.getAxisMotionValue(e).get()||0;if(Y.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[e];if(r){let e=i_(r);t=parseFloat(t)/100*e}}}this.originPoint[e]=t}),s&&eM.update(()=>s(e,t),!1,!0);let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:s,onDrag:n}=this.getProps();if(!i&&!this.openGlobalLock)return;let{offset:a}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),n&&n(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>ri(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:rx(this.visualElement)})}stop(e,t){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:s}=this.getProps();s&&eM.update(()=>s(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:r}=this.getProps();if(!i||!rw(e,r,this.currentDirection))return;let s=this.getAxisMotionValue(e),n=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(n=function(e,{min:t,max:i},r){return void 0!==t&&e<t?e=r?tS(t,e,r.min):Math.max(e,t):void 0!==i&&e>i&&(e=r?tS(i,e,r.max):Math.min(e,i)),e}(n,this.constraints[e],this.elastic[e])),s.set(n)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,s=this.constraints;t&&p(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(e,{top:t,left:i,bottom:r,right:s}){return{x:i3(e.x,i,s),y:i3(e.y,t,r)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:i6(e,"left","right"),y:i6(e,"top","bottom")}}(i),s!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&ri(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!p(t))return!1;let r=t.current;eT(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let n=function(e,t,i){let r=rg(e,i),{scroll:s}=t;return s&&(rd(r.x,s.offset.x),rd(r.y,s.offset.y)),r}(r,s.root,this.visualElement.getTransformPagePoint()),a={x:i4((e=s.layout.layoutBox).x,n.x),y:i4(e.y,n.y)};if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=rr(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:r,dragTransition:s,dragSnapToOrigin:n,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(ri(a=>{if(!rw(a,t,this.currentDirection))return;let l=o&&o[a]||{};n&&(l={min:0,max:0});let h={type:"inertia",velocity:i?e[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return i.start(iw(e,i,0,t))}stopAnimation(){ri(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){ri(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){ri(t=>{let{drag:i}=this.getProps();if(!rw(t,i,this.currentDirection))return;let{projection:r}=this.visualElement,s=this.getAxisMotionValue(t);if(r&&r.layout){let{min:i,max:n}=r.layout.layoutBox[t];s.set(e[t]-tS(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!p(t)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};ri(e=>{let t=this.getAxisMotionValue(e);if(t){let i=t.get();r[e]=function(e,t){let i=.5,r=i_(e),s=i_(t);return s>r?i=tX(t.min,t.max-r,e.min):r>s&&(i=tX(e.min,e.max-s,t.min)),O(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),ri(t=>{if(!rw(t,e,null))return;let i=this.getAxisMotionValue(t),{min:s,max:n}=this.constraints[t];i.set(tS(s,n,r[t]))})}addListeners(){if(!this.visualElement.current)return;rv.set(this.visualElement,this);let e=eO(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();p(e)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),t();let s=eL(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(ri(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{s(),e(),r(),n&&n()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:s=!1,dragElastic:n=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:r,dragConstraints:s,dragElastic:n,dragMomentum:a}}}function rw(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class rj extends eq{constructor(e){super(e),this.removeGroupControls=eT,this.removeListeners=eT,this.controls=new rb(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eT}unmount(){this.removeGroupControls(),this.removeListeners()}}let rP=e=>(t,i)=>{e&&eM.update(()=>e(t,i))};class rk extends eq{constructor(){super(...arguments),this.removePointerDownListener=eT}onPointerDown(e){this.session=new iK(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rx(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rP(e),onStart:rP(t),onMove:i,onEnd:(e,t)=>{delete this.session,r&&eM.update(()=>r(e,t))}}}mount(){this.removePointerDownListener=eO(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let rN={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rT(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let rA={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!_.test(e))return e;e=parseFloat(e)}let i=rT(e,t.target.x),r=rT(e,t.target.y);return`${i}% ${r}%`}};class rS extends s.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:r}=this.props,{projection:s}=e;Object.assign(S,rV),s&&(t.group&&t.group.add(s),i&&i.register&&r&&i.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),rN.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:r,isPresent:s}=this.props,n=i.projection;return n&&(n.isPresent=s,r||e.layoutDependency!==t||void 0===t?n.willUpdate():this.safeToRemove(),e.isPresent===s||(s?n.promote():n.relegate()||eM.postRender(()=>{let e=n.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rM(e){let[t,i]=function(){let e=(0,s.useContext)(o);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:i,register:r}=e,n=(0,s.useId)();return(0,s.useEffect)(()=>r(n),[]),!t&&i?[!1,()=>i&&i(n)]:[!0]}(),r=(0,s.useContext)(P);return s.createElement(rS,{...e,layoutGroup:r,switchLayoutGroup:(0,s.useContext)(k),isPresent:t,safeToRemove:i})}let rV={borderRadius:{...rA,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rA,borderTopRightRadius:rA,borderBottomLeftRadius:rA,borderBottomRightRadius:rA,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let r=tW.parse(e);if(r.length>5)return e;let s=tW.createTransformer(e),n="number"!=typeof r[0]?1:0,a=i.x.scale*t.x,o=i.y.scale*t.y;r[0+n]/=a,r[1+n]/=o;let l=tS(a,o,.5);return"number"==typeof r[2+n]&&(r[2+n]/=l),"number"==typeof r[3+n]&&(r[3+n]/=l),s(r)}}},rE=["TopLeft","TopRight","BottomLeft","BottomRight"],rC=rE.length,rD=e=>"string"==typeof e?parseFloat(e):e,rR=e=>"number"==typeof e||_.test(e);function rL(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rB=rI(0,.5,tp),rF=rI(.5,.95,eT);function rI(e,t,i){return r=>r<e?0:r>t?1:i(tX(e,t,r))}function rO(e,t){e.min=t.min,e.max=t.max}function rU(e,t){rO(e.x,t.x),rO(e.y,t.y)}function rz(e,t,i,r,s){return e-=t,e=r+1/i*(e-r),void 0!==s&&(e=r+1/s*(e-r)),e}function r$(e,t,[i,r,s],n,a){!function(e,t=0,i=1,r=.5,s,n=e,a=e){if(Y.test(t)&&(t=parseFloat(t),t=tS(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=tS(n.min,n.max,r);e===n&&(o-=t),e.min=rz(e.min,t,i,o,s),e.max=rz(e.max,t,i,o,s)}(e,t[i],t[r],t[s],t.scale,n,a)}let rW=["x","scaleX","originX"],rH=["y","scaleY","originY"];function rG(e,t,i,r){r$(e.x,t,rW,i?i.x:void 0,r?r.x:void 0),r$(e.y,t,rH,i?i.y:void 0,r?r.y:void 0)}function rK(e){return 0===e.translate&&1===e.scale}function rq(e){return rK(e.x)&&rK(e.y)}function rZ(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function rX(e){return i_(e.x)/i_(e.y)}class rY{constructor(){this.members=[]}add(e){ik(this.members,e),e.scheduleRender()}remove(e){if(iN(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function r_(e,t,i){let r="",s=e.x.translate/t.x,n=e.y.translate/t.y;if((s||n)&&(r=`translate3d(${s}px, ${n}px, 0) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),i){let{rotate:e,rotateX:t,rotateY:s}=i;e&&(r+=`rotate(${e}deg) `),t&&(r+=`rotateX(${t}deg) `),s&&(r+=`rotateY(${s}deg) `)}let a=e.x.scale*t.x,o=e.y.scale*t.y;return(1!==a||1!==o)&&(r+=`scale(${a}, ${o})`),r||"none"}let rJ=(e,t)=>e.depth-t.depth;class rQ{constructor(){this.children=[],this.isDirty=!1}add(e){ik(this.children,e),this.isDirty=!0}remove(e){iN(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rJ),this.isDirty=!1,this.children.forEach(e)}}let r0=["","X","Y","Z"],r1={visibility:"hidden"},r2=0,r5={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function r3({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(e={},i=null==t?void 0:t()){this.id=r2++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,r5.totalNodes=r5.resolvedTargetDeltas=r5.recalculatedProjection=0,this.nodes.forEach(r8),this.nodes.forEach(ss),this.nodes.forEach(sn),this.nodes.forEach(r9),window.MotionDebug&&window.MotionDebug.record(r5)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rQ)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new iT),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:r,layout:s,visualElement:n}=this.options;if(n&&!n.current&&n.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(s||r)&&(this.isLayoutDirty=!0),e){let i;let r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=performance.now(),r=({timestamp:t})=>{let s=t-i;s>=250&&(eV(r),e(s-250))};return eM.read(r,!0),()=>eV(r)}(r,0),rN.hasAnimatedSinceResize&&(rN.hasAnimatedSinceResize=!1,this.nodes.forEach(sr))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&n&&(r||s)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||sc,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!rZ(this.targetLayout,r)||i,h=!t&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,h);let t={...iv(s,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||sr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,eV(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(sa),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(se);return}this.isUpdating||this.nodes.forEach(st),this.isUpdating=!1,this.nodes.forEach(si),this.nodes.forEach(r4),this.nodes.forEach(r6),this.clearAllSnapshots();let e=performance.now();eE.delta=O(0,1e3/60,e-eE.timestamp),eE.timestamp=e,eE.isProcessing=!0,eC.update.process(eE),eC.preRender.process(eE),eC.render.process(eE),eE.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(r7),this.sharedNodes.forEach(so)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eM.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eM.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:r(this.instance),offset:i(this.instance)})}resetTransform(){if(!s)return;let e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!rq(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,n=r!==this.prevTransformTemplateValue;e&&(t||ra(this.latestValues)||n)&&(s(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),r=this.removeElementScroll(i);return e&&(r=this.removeTransform(r)),sm((t=r).x),sm(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return rt();let t=e.measureViewportBox(),{scroll:i}=this.root;return i&&(rd(t.x,i.offset.x),rd(t.y,i.offset.y)),t}removeElementScroll(e){let t=rt();rU(t,e);for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:s,options:n}=r;if(r!==this.root&&s&&n.layoutScroll){if(s.isRoot){rU(t,e);let{scroll:i}=this.root;i&&(rd(t.x,-i.offset.x),rd(t.y,-i.offset.y))}rd(t.x,s.offset.x),rd(t.y,s.offset.y)}}return t}applyTransform(e,t=!1){let i=rt();rU(i,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&ry(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),ra(r.latestValues)&&ry(i,r.latestValues)}return ra(this.latestValues)&&ry(i,this.latestValues),i}removeTransform(e){let t=rt();rU(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!ra(i.latestValues))continue;rn(i.latestValues)&&i.updateSnapshot();let r=rt();rU(r,i.measurePageBox()),rG(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return ra(this.latestValues)&&rG(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eE.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,i,r,s;let n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==n;if(!(e||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:o,layoutId:l}=this.options;if(this.layout&&(o||l)){if(this.resolvedRelativeTargetAt=eE.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rt(),this.relativeTargetOrigin=rt(),i5(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rU(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=rt(),this.targetWithTransforms=rt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,r=this.relativeTarget,s=this.relativeParent.target,i1(i.x,r.x,s.x),i1(i.y,r.y,s.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rU(this.target,this.layout.layoutBox),ru(this.target,this.targetDelta)):rU(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rt(),this.relativeTargetOrigin=rt(),i5(this.relativeTargetOrigin,this.target,e.target),rU(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}r5.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||rn(this.parent.latestValues)||ro(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),i=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(r=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===eE.timestamp&&(r=!1),r)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;rU(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,o=this.treeScale.y;(function(e,t,i,r=!1){let s,n;let a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){n=(s=i[o]).projectionDelta;let a=s.instance;(!a||!a.style||"contents"!==a.style.display)&&(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&ry(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,ru(e,n)),r&&ra(s.latestValues)&&ry(e,s.latestValues))}t.x=rc(t.x),t.y=rc(t.y)}})(this.layoutCorrected,this.treeScale,this.path,i),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox);let{target:l}=t;if(!l){this.projectionTransform&&(this.projectionDelta=i7(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=i7(),this.projectionDeltaWithTransform=i7());let h=this.projectionTransform;i0(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=r_(this.projectionDelta,this.treeScale),(this.projectionTransform!==h||this.treeScale.x!==a||this.treeScale.y!==o)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),r5.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e,t=!1){let i;let r=this.snapshot,s=r?r.latestValues:{},n={...this.latestValues},a=i7();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=rt(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,c=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(su));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(sl(a.x,e.x,r),sl(a.y,e.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,d,p,m;i5(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,sh(p.x,m.x,o.x,r),sh(p.y,m.y,o.y,r),i&&(h=this.relativeTarget,d=i,h.x.min===d.x.min&&h.x.max===d.x.max&&h.y.min===d.y.min&&h.y.max===d.y.max)&&(this.isProjectionDirty=!1),i||(i=rt()),rU(i,this.relativeTarget)}l&&(this.animationValues=n,function(e,t,i,r,s,n){s?(e.opacity=tS(0,void 0!==i.opacity?i.opacity:1,rB(r)),e.opacityExit=tS(void 0!==t.opacity?t.opacity:1,0,rF(r))):n&&(e.opacity=tS(void 0!==t.opacity?t.opacity:1,void 0!==i.opacity?i.opacity:1,r));for(let s=0;s<rC;s++){let n=`border${rE[s]}Radius`,a=rL(t,n),o=rL(i,n);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||rR(a)===rR(o)?(e[n]=Math.max(tS(rD(a),rD(o),r),0),(Y.test(o)||Y.test(a))&&(e[n]+="%")):e[n]=o)}(t.rotate||i.rotate)&&(e.rotate=tS(t.rotate||0,i.rotate||0,r))}(n,s,this.latestValues,r,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(eV(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eM.update(()=>{rN.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,i){let r=C(0)?0:iV(0);return r.start(iw("",r,1e3,i)),r.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:r,latestValues:s}=e;if(t&&i&&r){if(this!==e&&this.layout&&r&&sf(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||rt();let t=i_(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let r=i_(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+r}rU(t,i),ry(t,s),i0(this.projectionDeltaWithTransform,this.layoutCorrected,t,s)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rY),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.rotate||i.rotateX||i.rotateY||i.rotateZ)&&(t=!0),!t)return;let r={};for(let t=0;t<r0.length;t++){let s="rotate"+r0[t];i[s]&&(r[s]=i[s],e.setStaticValue(s,0))}for(let t in e.render(),r)e.setStaticValue(t,r[t]);e.scheduleRender()}getProjectionStyles(e){var t,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return r1;let r={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=ek(null==e?void 0:e.pointerEvents)||"",r.transform=s?s(this.latestValues,""):"none",r;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=ek(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!ra(this.latestValues)&&(t.transform=s?s({},""):"none",this.hasProjected=!1),t}let a=n.animationValues||n.latestValues;this.applyTransformsToTarget(),r.transform=r_(this.projectionDeltaWithTransform,this.treeScale,a),s&&(r.transform=s(a,r.transform));let{x:o,y:l}=this.projectionDelta;for(let e in r.transformOrigin=`${100*o.origin}% ${100*l.origin}% 0`,n.animationValues?r.opacity=n===this?null!==(i=null!==(t=a.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:r.opacity=n===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0,S){if(void 0===a[e])continue;let{correct:t,applyTo:i}=S[e],s="none"===r.transform?a[e]:t(a[e],n);if(i){let e=i.length;for(let t=0;t<e;t++)r[i[t]]=s}else r[e]=s}return this.options.layoutId&&(r.pointerEvents=n===this?ek(null==e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(se),this.root.sharedNodes.clear()}}}function r4(e){e.updateLayout()}function r6(e){var t;let i=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&i&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:r}=e.layout,{animationType:s}=e.options,n=i.source!==e.layout.source;"size"===s?ri(e=>{let r=n?i.measuredBox[e]:i.layoutBox[e],s=i_(r);r.min=t[e].min,r.max=r.min+s}):sf(s,i.layoutBox,t)&&ri(r=>{let s=n?i.measuredBox[r]:i.layoutBox[r],a=i_(t[r]);s.max=s.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)});let a=i7();i0(a,t,i.layoutBox);let o=i7();n?i0(o,e.applyTransform(r,!0),i.measuredBox):i0(o,t,i.layoutBox);let l=!rq(a),h=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:s,layout:n}=r;if(s&&n){let a=rt();i5(a,i.layoutBox,s.layoutBox);let o=rt();i5(o,t,n.layoutBox),rZ(a,o)||(h=!0),r.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:i,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:h})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function r8(e){r5.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function r9(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function r7(e){e.clearSnapshot()}function se(e){e.clearMeasurements()}function st(e){e.isLayoutDirty=!1}function si(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function sr(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ss(e){e.resolveTargetDelta()}function sn(e){e.calcProjection()}function sa(e){e.resetRotation()}function so(e){e.removeLeadSnapshot()}function sl(e,t,i){e.translate=tS(t.translate,0,i),e.scale=tS(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function sh(e,t,i,r){e.min=tS(t.min,i.min,r),e.max=tS(t.max,i.max,r)}function su(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let sc={duration:.45,ease:[.4,0,.1,1]},sd=e=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(e),sp=sd("applewebkit/")&&!sd("chrome/")?Math.round:eT;function sm(e){e.min=sp(e.min),e.max=sp(e.max)}function sf(e,t,i){return"position"===e||"preserve-aspect"===e&&!iJ(rX(t),rX(i),.2)}let sy=r3({attachResizeListener:(e,t)=>eL(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),sg={current:void 0},sx=r3({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!sg.current){let e=new sy({});e.mount(window),e.setOptions({layoutScroll:!0}),sg.current=e}return sg.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position}),sv=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function sb(e,t,i=1){eT(i<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(e){let t=sv.exec(e);if(!t)return[,];let[,i,r]=t;return[i,r]}(e);if(!r)return;let n=window.getComputedStyle(t).getPropertyValue(r);if(n){let e=n.trim();return iP(e)?parseFloat(e):e}return F(s)?sb(s,t,i+1):s}let sw=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),sj=e=>sw.has(e),sP=e=>Object.keys(e).some(sj),sk=e=>e===U||e===_,sN=(e,t)=>parseFloat(e.split(", ")[t]),sT=(e,t)=>(i,{transform:r})=>{if("none"===r||!r)return 0;let s=r.match(/^matrix3d\((.+)\)$/);if(s)return sN(s[1],t);{let t=r.match(/^matrix\((.+)\)$/);return t?sN(t[1],e):0}},sA=new Set(["x","y","z"]),sS=M.filter(e=>!sA.has(e)),sM={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:sT(4,13),y:sT(5,14)};sM.translateX=sM.x,sM.translateY=sM.y;let sV=(e,t,i)=>{let r=t.measureViewportBox(),s=getComputedStyle(t.current),{display:n}=s,a={};"none"===n&&t.setStaticValue("display",e.display||"block"),i.forEach(e=>{a[e]=sM[e](r,s)}),t.render();let o=t.measureViewportBox();return i.forEach(i=>{let r=t.getValue(i);r&&r.jump(a[i]),e[i]=sM[i](o,s)}),e},sE=(e,t,i={},r={})=>{t={...t},r={...r};let s=Object.keys(t).filter(sj),n=[],a=!1,o=[];if(s.forEach(s=>{let l;let h=e.getValue(s);if(!e.hasValue(s))return;let u=i[s],c=iD(u),d=t[s];if(ew(d)){let e=d.length,t=null===d[0]?1:0;c=iD(u=d[t]);for(let i=t;i<e&&null!==d[i];i++)l?eT(iD(d[i])===l,"All keyframes must be of the same type"):eT((l=iD(d[i]))===c||sk(c)&&sk(l),"Keyframes must be of the same dimension as the current value")}else l=iD(d);if(c!==l){if(sk(c)&&sk(l)){let e=h.get();"string"==typeof e&&h.set(parseFloat(e)),"string"==typeof d?t[s]=parseFloat(d):Array.isArray(d)&&l===_&&(t[s]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===u||0===d)?0===u?h.set(l.transform(u)):t[s]=c.transform(d):(a||(n=function(e){let t=[];return sS.forEach(i=>{let r=e.getValue(i);void 0!==r&&(t.push([i,r.get()]),r.set(i.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),a=!0),o.push(s),r[s]=void 0!==r[s]?r[s]:t[s],h.jump(d))}}),!o.length)return{target:t,transitionEnd:r};{let i=o.indexOf("height")>=0?window.pageYOffset:null,s=sV(t,e,o);return n.length&&n.forEach(([t,i])=>{e.getValue(t).set(i)}),e.render(),l&&null!==i&&window.scrollTo({top:i}),{target:s,transitionEnd:r}}},sC=(e,t,i,r)=>{let s=function(e,{...t},i){let r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:i};for(let s in i&&(i={...i}),e.values.forEach(e=>{let t=e.get();if(!F(t))return;let i=sb(t,r);i&&e.set(i)}),t){let e=t[s];if(!F(e))continue;let n=sb(e,r);n&&(t[s]=n,i||(i={}),void 0===i[s]&&(i[s]=e))}return{target:t,transitionEnd:i}}(e,t,r);return function(e,t,i,r){return sP(t)?sE(e,t,i,r):{target:t,transitionEnd:r}}(e,t=s.target,i,r=s.transitionEnd)},sD={current:null},sR={current:!1},sL=new WeakMap,sB=Object.keys(j),sF=sB.length,sI=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],sO=g.length;class sU{constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:r,visualState:s},n={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>eM.render(this.render,!1,!0);let{latestValues:a,renderState:o}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=o,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=n,this.isControllingVariants=x(t),this.isVariantNode=v(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:l,...h}=this.scrapeMotionValuesFromProps(t,{});for(let e in h){let t=h[e];void 0!==a[e]&&C(t)&&(t.set(a[e],!1),ij(l)&&l.add(e))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,sL.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),sR.current||function(){if(sR.current=!0,l){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>sD.current=e.matches;e.addListener(t),t()}else sD.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sD.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in sL.delete(this.current),this.projection&&this.projection.unmount(),eV(this.notifyUpdate),eV(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let i=V.has(e),r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eM.update(this.notifyUpdate,!1,!0),i&&this.projection&&(this.projection.isTransformDirty=!0)}),s=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{r(),s()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures({children:e,...t},i,r,s){let n,a;for(let e=0;e<sF;e++){let i=sB[e],{isEnabled:r,Feature:s,ProjectionNode:o,MeasureLayout:l}=j[i];o&&(n=o),r(t)&&(!this.features[i]&&s&&(this.features[i]=new s(this)),l&&(a=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&n){this.projection=new n(this.latestValues,this.parent&&this.parent.projection);let{layoutId:e,layout:i,drag:r,dragConstraints:a,layoutScroll:o,layoutRoot:l}=t;this.projection.setOptions({layoutId:e,layout:i,alwaysMeasureLayout:!!r||a&&p(a),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof i?i:"both",initialPromotionConfig:s,layoutScroll:o,layoutRoot:l})}return a}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rt()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<sI.length;t++){let i=sI[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(e,t,i){let{willChange:r}=t;for(let s in t){let n=t[s],a=i[s];if(C(n))e.addValue(s,n),ij(r)&&r.add(s);else if(C(a))e.addValue(s,iV(n,{owner:e})),ij(r)&&r.remove(s);else if(a!==n){if(e.hasValue(s)){let t=e.getValue(s);t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(s);e.addValue(s,iV(void 0!==t?t:n,{owner:e}))}}}for(let r in i)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}let t={};for(let e=0;e<sO;e++){let i=g[e],r=this.props[i];(m(r)||!1===r)&&(t[i]=r)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=iV(t,{owner:this}),this.addValue(e,i)),i}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:i}=this.props,r="string"==typeof i||"object"==typeof i?null===(t=eb(this.props,i))||void 0===t?void 0:t[e]:void 0;if(i&&void 0!==r)return r;let s=this.getBaseTargetFromProps(this.props,e);return void 0===s||C(s)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:s}on(e,t){return this.events[e]||(this.events[e]=new iT),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class sz extends sU{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...i},{transformValues:r},s){let n=function(e,t,i){let r={};for(let s in e){let e=function(e,t){if(t)return(t[e]||t.default||t).from}(s,t);if(void 0!==e)r[s]=e;else{let e=i.getValue(s);e&&(r[s]=e.get())}}return r}(i,e||{},this);if(r&&(t&&(t=r(t)),i&&(i=r(i)),n&&(n=r(n))),s){!function(e,t,i){var r,s;let n=Object.keys(t).filter(t=>!e.hasValue(t)),a=n.length;if(a)for(let o=0;o<a;o++){let a=n[o],l=t[a],h=null;Array.isArray(l)&&(h=l[0]),null===h&&(h=null!==(s=null!==(r=i[a])&&void 0!==r?r:e.readValue(a))&&void 0!==s?s:t[a]),null!=h&&("string"==typeof h&&(iP(h)||ix(h))?h=parseFloat(h):!iL(h)&&tW.test(l)&&(h=ig(a,l)),e.addValue(a,iV(h,{owner:e})),void 0===i[a]&&(i[a]=h),null!==h&&e.setBaseTarget(a,h))}}(this,i,n);let e=sC(this,i,n,t);t=e.transitionEnd,i=e.target}return{transition:e,transitionEnd:t,...i}}}class s$ extends sz{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(V.has(t)){let e=iy(t);return e&&e.default||0}{let i=window.getComputedStyle(e),r=(B(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return rg(e,t)}build(e,t,i,r){er(e,t,i,r.transformTemplate)}scrapeMotionValuesFromProps(e,t){return ex(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;C(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}renderInstance(e,t,i,r){ef(e,t,i,r)}}class sW extends sz{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(V.has(t)){let e=iy(t);return e&&e.default||0}return t=ey.has(t)?t:c(t),e.getAttribute(t)}measureInstanceViewportBox(){return rt()}scrapeMotionValuesFromProps(e,t){return ev(e,t)}build(e,t,i,r){ed(e,t,i,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,i,r){eg(e,t,i,r)}mount(e){this.isSVGTag=em(e.tagName),super.mount(e)}}let sH=(e,t)=>A(e)?new sW(t,{enableHardwareAcceleration:!1}):new s$(t,{enableHardwareAcceleration:!0}),sG={animation:{Feature:i$},exit:{Feature:iH},inView:{Feature:e4},tap:{Feature:eQ},focus:{Feature:eY},hover:{Feature:eX},pan:{Feature:rk},drag:{Feature:rj,ProjectionNode:sx,MeasureLayout:rM},layout:{ProjectionNode:sx,MeasureLayout:rM}},sK=function(e){function t(t,i={}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:r,Component:c}){e&&function(e){for(let t in e)j[t]={...j[t],...e[t]}}(e);let f=(0,s.forwardRef)(function(f,y){var g;let v;let w={...(0,s.useContext)(n),...f,layoutId:function({layoutId:e}){let t=(0,s.useContext)(P).id;return t&&void 0!==e?t+"-"+e:e}(f)},{isStatic:j}=w,N=function(e){let{initial:t,animate:i}=function(e,t){if(x(e)){let{initial:t,animate:i}=e;return{initial:!1===t||m(t)?t:void 0,animate:m(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,s.useContext)(a));return(0,s.useMemo)(()=>({initial:t,animate:i}),[b(t),b(i)])}(f),T=r(f,j);if(!j&&l){N.visualElement=function(e,t,i,r){let{visualElement:l}=(0,s.useContext)(a),c=(0,s.useContext)(u),p=(0,s.useContext)(o),m=(0,s.useContext)(n).reducedMotion,f=(0,s.useRef)();r=r||c.renderer,!f.current&&r&&(f.current=r(e,{visualState:t,parent:l,props:i,presenceContext:p,blockInitialAnimation:!!p&&!1===p.initial,reducedMotionConfig:m}));let y=f.current;(0,s.useInsertionEffect)(()=>{y&&y.update(i,p)});let g=(0,s.useRef)(!!(i[d]&&!window.HandoffComplete));return h(()=>{y&&(y.render(),g.current&&y.animationState&&y.animationState.animateChanges())}),(0,s.useEffect)(()=>{y&&(y.updateFeatures(),!g.current&&y.animationState&&y.animationState.animateChanges(),g.current&&(g.current=!1,window.HandoffComplete=!0))}),y}(c,T,w,t);let i=(0,s.useContext)(k),r=(0,s.useContext)(u).strict;N.visualElement&&(v=N.visualElement.loadFeatures(w,r,e,i))}return s.createElement(a.Provider,{value:N},v&&N.visualElement?s.createElement(v,{visualElement:N.visualElement,...w}):null,i(c,f,(g=N.visualElement,(0,s.useCallback)(e=>{e&&T.mount&&T.mount(e),g&&(e?g.mount(e):g.unmount()),y&&("function"==typeof y?y(e):p(y)&&(y.current=e))},[g])),T,j,N.visualElement))});return f[N]=c,f}(e(t,i))}if("undefined"==typeof Proxy)return t;let i=new Map;return new Proxy(t,{get:(e,r)=>(i.has(r)||i.set(r,t(r)),i.get(r))})}((e,t)=>(function(e,{forwardMotionProps:t=!1},i,r){return{...A(e)?eD:eR,preloadedFeatures:i,useRender:function(e=!1){return(t,i,r,{latestValues:n},a)=>{let o=(A(t)?function(e,t,i,r){let n=(0,s.useMemo)(()=>{let i=ep();return ed(i,t,{enableHardwareAcceleration:!1},em(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};en(t,e.style,e),n.style={...t,...n.style}}return n}:function(e,t,i){let r={},n=function(e,t,i){let r=e.style||{},n={};return en(n,r,e),Object.assign(n,function({transformTemplate:e},t,i){return(0,s.useMemo)(()=>{let r=es();return er(r,t,{enableHardwareAcceleration:!i},e),Object.assign({},r.vars,r.style)},[t])}(e,t,i)),e.transformValues?e.transformValues(n):n}(e,t,i);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(i,n,a,t),l={...function(e,t,i){let r={};for(let s in e)("values"!==s||"object"!=typeof e.values)&&(el(s)||!0===i&&eo(s)||!t&&!eo(s)||e.draggable&&s.startsWith("onDrag"))&&(r[s]=e[s]);return r}(i,"string"==typeof t,e),...o,ref:r},{children:h}=i,u=(0,s.useMemo)(()=>C(h)?h.get():h,[h]);return(0,s.createElement)(t,{...l,children:u})}}(t),createVisualElement:r,Component:e}})(e,t,sG,sH));var sq={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let sZ=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),sX=(e,t)=>{let i=(0,s.forwardRef)(({color:i="currentColor",size:r=24,strokeWidth:n=2,absoluteStrokeWidth:a,children:o,...l},h)=>(0,s.createElement)("svg",{ref:h,...sq,width:r,height:r,stroke:i,strokeWidth:a?24*Number(n)/Number(r):n,className:`lucide lucide-${sZ(e)}`,...l},[...t.map(([e,t])=>(0,s.createElement)(e,t)),...(Array.isArray(o)?o:[o])||[]]));return i.displayName=`${e}`,i},sY=sX("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),s_=sX("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]),sJ=sX("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),sQ=sX("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),s0=()=>(0,r.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 opacity-10",children:(0,r.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 1000 1000",children:[r.jsx("defs",{children:r.jsx("pattern",{id:"mesh",x:"0",y:"0",width:"100",height:"100",patternUnits:"userSpaceOnUse",children:r.jsx("circle",{cx:"50",cy:"50",r:"2",fill:"currentColor",className:"text-blue-400"})})}),r.jsx("rect",{width:"100%",height:"100%",fill:"url(#mesh)"}),r.jsx(sK.line,{x1:"100",y1:"100",x2:"300",y2:"200",stroke:"currentColor",strokeWidth:"1",className:"text-blue-400 mesh-connect",initial:{pathLength:0},animate:{pathLength:1},transition:{duration:2,repeat:1/0,repeatType:"reverse"}}),r.jsx(sK.line,{x1:"300",y1:"200",x2:"500",y2:"150",stroke:"currentColor",strokeWidth:"1",className:"text-blue-400 mesh-connect",initial:{pathLength:0},animate:{pathLength:1},transition:{duration:2,delay:.5,repeat:1/0,repeatType:"reverse"}}),r.jsx(sK.line,{x1:"500",y1:"150",x2:"700",y2:"300",stroke:"currentColor",strokeWidth:"1",className:"text-blue-400 mesh-connect",initial:{pathLength:0},animate:{pathLength:1},transition:{duration:2,delay:1,repeat:1/0,repeatType:"reverse"}})]})}),r.jsx("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-8",children:[(0,r.jsxs)(sK.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.8,delay:.2},className:"flex items-center justify-center space-x-3",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center",children:r.jsx(sY,{className:"w-8 h-8 text-white"})}),r.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"})]}),(0,r.jsxs)("h1",{className:"text-5xl md:text-7xl font-bold font-mono text-white",children:["krtr",r.jsx("span",{className:"text-blue-400",children:"*"}),"mesh"]})]}),(0,r.jsxs)(sK.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed",children:[r.jsx("span",{className:"gradient-text font-semibold",children:"Decentralized, encrypted, offline-first messaging"}),r.jsx("br",{}),"for the post-platform era."]}),r.jsx(sK.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"text-lg text-gray-400 max-w-2xl mx-auto font-mono",children:"Built from the blackout. Inspired by the streets. Whispered across devices."}),r.jsx(sK.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"flex flex-wrap justify-center gap-8 mt-12",children:[{icon:s_,label:"End-to-End Encrypted",color:"text-green-400"},{icon:sJ,label:"Zero-Knowledge Proofs",color:"text-yellow-400"},{icon:sQ,label:"Mesh Networking",color:"text-blue-400"},{icon:sY,label:"Offline-First",color:"text-purple-400"}].map((e,t)=>(0,r.jsxs)(sK.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1+.1*t},className:"flex flex-col items-center space-y-2 group cursor-pointer",children:[r.jsx("div",{className:`p-4 rounded-xl bg-dark-800 border border-dark-700 group-hover:border-blue-500 transition-colors ${e.color}`,children:r.jsx(e.icon,{className:"w-6 h-6"})}),r.jsx("span",{className:"text-sm text-gray-400 group-hover:text-white transition-colors",children:e.label})]},e.label))}),(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1.2},className:"flex flex-col sm:flex-row gap-4 justify-center mt-12",children:[r.jsx("button",{className:"px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg",children:"Download App"}),r.jsx("button",{className:"px-8 py-4 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300",children:"Learn More"})]}),r.jsx(sK.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:1.5},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:r.jsx(sK.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-gray-600 rounded-full flex justify-center",children:r.jsx(sK.div,{animate:{y:[0,12,0]},transition:{duration:2,repeat:1/0},className:"w-1 h-3 bg-blue-400 rounded-full mt-2"})})})]})})]}),s1=sX("Network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]]),s2=sX("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),s5=sX("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),s3=sX("Battery",[["rect",{width:"16",height:"10",x:"2",y:"7",rx:"2",ry:"2",key:"1w10f2"}],["line",{x1:"22",x2:"22",y1:"11",y2:"13",key:"4dh1rd"}]]),s4=()=>{let e=[{icon:s1,title:"Mesh Networking",description:"Multi-hop routing with TTL-based forwarding. Connect up to 20 peers simultaneously with automatic discovery via Bluetooth LE.",details:["Automatic peer discovery","Multi-hop routing (max 7 hops)","Connection management","Loop prevention"],color:"from-blue-500 to-cyan-500"},{icon:s_,title:"End-to-End Encryption",description:"Military-grade encryption with X25519 key exchange, AES-256-GCM, and Ed25519 signatures for message authenticity.",details:["X25519 key exchange","AES-256-GCM encryption","Ed25519 signatures","Forward secrecy"],color:"from-green-500 to-emerald-500"},{icon:sJ,title:"Zero-Knowledge Proofs",description:"Anonymous authentication and private reputation using Noir ZK circuits. Prove membership without revealing identity.",details:["Anonymous authentication","Private reputation","Selective disclosure","Group authorization"],color:"from-yellow-500 to-orange-500"},{icon:s2,title:"Store-and-Forward",description:"Offline message delivery with intelligent routing. Messages reach their destination even when peers are temporarily offline.",details:["Offline message delivery","Message persistence","Intelligent routing","Delivery confirmation"],color:"from-purple-500 to-pink-500"},{icon:s5,title:"Privacy Features",description:"Cover traffic generation and timing randomization prevent traffic analysis. Emergency wipe capability for security.",details:["Cover traffic generation","Timing randomization","Ephemeral identities","Emergency wipe"],color:"from-indigo-500 to-purple-500"},{icon:s3,title:"Battery Optimization",description:"4-tier power management with adaptive connection limits. LZ4 compression saves 30-70% bandwidth.",details:["4-tier power management","Adaptive connections","LZ4 compression","Binary protocol"],color:"from-red-500 to-pink-500"}];return(0,r.jsxs)("section",{className:"py-20 bg-dark-900 relative overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 opacity-5",children:r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"})}),(0,r.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:["Built for ",r.jsx("span",{className:"gradient-text",children:"Privacy"})," & ",r.jsx("span",{className:"gradient-text",children:"Performance"})]}),r.jsx("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto",children:"KRTR Mesh combines cutting-edge cryptography with mesh networking to deliver secure, private, and resilient communication that works anywhere."})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map((e,t)=>r.jsx(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},className:"group",children:(0,r.jsxs)("div",{className:"bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105",children:[r.jsx("div",{className:`w-16 h-16 rounded-xl bg-gradient-to-r ${e.color} p-4 mb-6 group-hover:scale-110 transition-transform duration-300`,children:r.jsx(e.icon,{className:"w-8 h-8 text-white"})}),r.jsx("h3",{className:"text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors",children:e.title}),r.jsx("p",{className:"text-gray-400 mb-6 leading-relaxed",children:e.description}),r.jsx("ul",{className:"space-y-2",children:e.details.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-center text-sm text-gray-500",children:[r.jsx("div",{className:"w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"}),e]},t))})]})},e.title))}),r.jsx(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.5},viewport:{once:!0},className:"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8",children:[{value:"7",label:"Max Hops",suffix:""},{value:"20",label:"Max Connections",suffix:""},{value:"70",label:"Bandwidth Savings",suffix:"%"},{value:"256",label:"AES Encryption",suffix:"-bit"}].map((e,t)=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-3xl md:text-4xl font-bold text-white mb-2",children:[e.value,r.jsx("span",{className:"text-blue-400",children:e.suffix})]}),r.jsx("div",{className:"text-gray-400 text-sm",children:e.label})]},e.label))})]})]})},s6=sX("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]),s8=sX("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]),s9=sX("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]),s7=sX("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),ne=()=>{let e=[{category:"Networking",icon:s6,color:"from-blue-500 to-cyan-500",technologies:[{name:"Bluetooth LE Mesh",description:"Low-energy mesh networking"},{name:"Multi-hop Routing",description:"TTL-based message forwarding"},{name:"Store-and-Forward",description:"Offline message delivery"},{name:"Connection Management",description:"Adaptive peer limits"}]},{category:"Cryptography",icon:s_,color:"from-green-500 to-emerald-500",technologies:[{name:"X25519 ECDH",description:"Key exchange with forward secrecy"},{name:"AES-256-GCM",description:"Authenticated encryption"},{name:"Ed25519",description:"Digital signatures"},{name:"Argon2id",description:"Password-based key derivation"}]},{category:"Zero-Knowledge",icon:sJ,color:"from-yellow-500 to-orange-500",technologies:[{name:"Noir Circuits",description:"ZK proof generation"},{name:"Anonymous Auth",description:"Identity-preserving verification"},{name:"Private Reputation",description:"Trust without exposure"},{name:"Selective Disclosure",description:"Prove without revealing"}]},{category:"Performance",icon:s8,color:"from-purple-500 to-pink-500",technologies:[{name:"LZ4 Compression",description:"30-70% bandwidth savings"},{name:"Binary Protocol",description:"Efficient message encoding"},{name:"Battery Optimization",description:"4-tier power management"},{name:"Message Fragmentation",description:"Large payload handling"}]}];return(0,r.jsxs)("section",{className:"py-20 bg-gradient-to-br from-dark-800 to-dark-900 relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[r.jsx("div",{className:"absolute top-20 left-20 w-32 h-32 bg-blue-500 rounded-full blur-3xl"}),r.jsx("div",{className:"absolute bottom-20 right-20 w-32 h-32 bg-purple-500 rounded-full blur-3xl"})]}),(0,r.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:[r.jsx("span",{className:"gradient-text",children:"Technical"})," Architecture"]}),r.jsx("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto",children:"Built on proven cryptographic primitives and modern networking protocols, KRTR Mesh delivers enterprise-grade security with consumer-friendly usability."})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-20",children:e.map((e,t)=>(0,r.jsxs)(sK.div,{initial:{opacity:0,x:t%2==0?-20:20},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},className:"bg-dark-800 border border-dark-700 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[r.jsx("div",{className:`w-12 h-12 rounded-xl bg-gradient-to-r ${e.color} p-3 mr-4`,children:r.jsx(e.icon,{className:"w-6 h-6 text-white"})}),r.jsx("h3",{className:"text-2xl font-bold text-white",children:e.category})]}),r.jsx("div",{className:"space-y-4",children:e.technologies.map((e,t)=>r.jsx("div",{className:"flex justify-between items-start",children:(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-white font-semibold",children:e.name}),r.jsx("div",{className:"text-gray-400 text-sm",children:e.description})]})},e.name))})]},e.category))}),(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"bg-dark-800 border border-dark-700 rounded-2xl p-8",children:[r.jsx("h3",{className:"text-2xl font-bold text-white mb-8 text-center",children:"System Architecture"}),r.jsx("div",{className:"space-y-4",children:[{name:"Application Layer",description:"SwiftUI / React Native UI",color:"bg-blue-500"},{name:"Service Layer",description:"Mesh, Crypto, ZK Services",color:"bg-green-500"},{name:"Protocol Layer",description:"KRTR Binary Protocol",color:"bg-yellow-500"},{name:"Transport Layer",description:"Bluetooth LE Abstraction",color:"bg-purple-500"},{name:"Hardware Layer",description:"iOS / Android / macOS",color:"bg-red-500"}].map((e,t)=>(0,r.jsxs)(sK.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5,delay:.1*t},viewport:{once:!0},className:"flex items-center p-4 bg-dark-700 rounded-xl hover:bg-dark-600 transition-colors",children:[r.jsx("div",{className:`w-4 h-4 ${e.color} rounded-full mr-4`}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("div",{className:"text-white font-semibold",children:e.name}),r.jsx("div",{className:"text-gray-400 text-sm",children:e.description})]}),r.jsx(s9,{className:"w-5 h-5 text-gray-500"})]},e.name))})]}),r.jsx(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8",children:[{title:"Forward Secrecy",description:"New keys generated each session ensure past communications remain secure even if current keys are compromised.",icon:s_},{title:"Zero Trust",description:"No central servers or authorities. Every message is cryptographically verified and authenticated.",icon:s7},{title:"Privacy by Design",description:"Cover traffic, timing randomization, and ephemeral identities prevent traffic analysis.",icon:sJ}].map((e,t)=>(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4",children:r.jsx(e.icon,{className:"w-8 h-8 text-white"})}),r.jsx("h4",{className:"text-xl font-bold text-white mb-2",children:e.title}),r.jsx("p",{className:"text-gray-400 text-sm leading-relaxed",children:e.description})]},e.title))})]})]})},nt=sX("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),ni=()=>{let e=[{title:"Dashboard",description:"Real-time network status, connection metrics, and quick actions all in one place.",icon:nt,features:["Network Status","Connected Peers","Battery Level","Power Mode"],mockup:{header:"Dashboard",content:[{label:"Network Status",value:"Active",status:"online"},{label:"Connected Peers",value:"7",status:"normal"},{label:"Battery Level",value:"85%",status:"good"},{label:"Power Mode",value:"Balanced",status:"normal"}]}},{title:"Mesh Network",description:"Visualize your mesh connections, peer discovery, and network topology in real-time.",icon:s1,features:["Peer Discovery","Connection Map","Signal Strength","Routing Info"],mockup:{header:"Mesh Network",content:[{label:"alice@mesh",value:"Connected",status:"online"},{label:"bob@mesh",value:"Discovering",status:"pending"},{label:"charlie@mesh",value:"Connected",status:"online"},{label:"diana@mesh",value:"Offline",status:"offline"}]}},{title:"Secure Chat",description:"End-to-end encrypted messaging with forward secrecy and anonymous authentication.",icon:s2,features:["E2E Encryption","Group Chats","File Sharing","Message Status"],mockup:{header:"General Chat",content:[{label:"alice",value:"Hey everyone! \uD83D\uDC4B",status:"received"},{label:"bob",value:"Network is looking good today",status:"received"},{label:"You",value:"Agreed! Zero issues so far",status:"sent"},{label:"charlie",value:"Love the new ZK features",status:"received"}]}},{title:"ZK Features",description:"Zero-knowledge proofs for anonymous authentication and private reputation systems.",icon:sJ,features:["Anonymous Auth","Private Reputation","Proof Generation","Verification"],mockup:{header:"ZK Dashboard",content:[{label:"Identity Proof",value:"Generated",status:"online"},{label:"Reputation Score",value:"Hidden",status:"private"},{label:"Group Membership",value:"Verified",status:"online"},{label:"Anonymous Mode",value:"Active",status:"private"}]}}];return(0,r.jsxs)("section",{className:"py-20 bg-dark-900 relative overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-900/10 to-purple-900/10"}),(0,r.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:["Experience ",r.jsx("span",{className:"gradient-text",children:"KRTR Mesh"})]}),r.jsx("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto",children:"Simple, intuitive interfaces that put powerful mesh networking and cryptographic privacy at your fingertips."})]}),r.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:e.map((e,t)=>r.jsx(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},className:"group",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row items-center gap-8",children:[(0,r.jsxs)("div",{className:"flex-1 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center",children:r.jsx(e.icon,{className:"w-6 h-6 text-white"})}),r.jsx("h3",{className:"text-2xl font-bold text-white",children:e.title})]}),r.jsx("p",{className:"text-gray-400 leading-relaxed",children:e.description}),r.jsx("div",{className:"grid grid-cols-2 gap-3",children:e.features.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),r.jsx("span",{className:"text-sm text-gray-300",children:e})]},e))})]}),r.jsx("div",{className:"flex-shrink-0",children:r.jsx("div",{className:"w-64 h-96 bg-dark-800 rounded-3xl border-4 border-dark-700 p-4 group-hover:border-blue-500/50 transition-all duration-300",children:(0,r.jsxs)("div",{className:"bg-dark-700 rounded-2xl h-full p-4 space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center text-xs text-gray-400",children:[r.jsx("span",{children:"9:41"}),(0,r.jsxs)("div",{className:"flex space-x-1",children:[r.jsx("div",{className:"w-4 h-2 bg-green-400 rounded-sm"}),r.jsx("div",{className:"w-4 h-2 bg-blue-400 rounded-sm"}),r.jsx("div",{className:"w-4 h-2 bg-gray-600 rounded-sm"})]})]}),r.jsx("div",{className:"text-center",children:r.jsx("h4",{className:"text-white font-semibold",children:e.mockup.header})}),r.jsx("div",{className:"space-y-3 flex-1",children:e.mockup.content.map((e,t)=>(0,r.jsxs)("div",{className:"flex justify-between items-center p-2 bg-dark-600 rounded-lg",children:[r.jsx("span",{className:"text-gray-300 text-sm",children:e.label}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:"text-white text-sm",children:e.value}),r.jsx("div",{className:`w-2 h-2 rounded-full ${"online"===e.status?"bg-green-400":"offline"===e.status?"bg-red-400":"pending"===e.status?"bg-yellow-400":"private"===e.status?"bg-purple-400":"sent"===e.status?"bg-blue-400":"received"===e.status?"bg-gray-400":"bg-gray-500"}`})]})]},t))}),r.jsx("div",{className:"flex justify-center",children:r.jsx("div",{className:"w-8 h-1 bg-gray-600 rounded-full"})})]})})})]})},e.title))}),(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.5},viewport:{once:!0},className:"mt-20 text-center",children:[r.jsx("h3",{className:"text-2xl font-bold text-white mb-8",children:"Available on All Platforms"}),r.jsx("div",{className:"flex justify-center space-x-8",children:[{name:"iOS",icon:"\uD83D\uDCF1"},{name:"Android",icon:"\uD83E\uDD16"},{name:"macOS",icon:"\uD83D\uDCBB"}].map(e=>(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[r.jsx("div",{className:"text-4xl",children:e.icon}),r.jsx("span",{className:"text-gray-400",children:e.name})]},e.name))})]})]})]})},nr=sX("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),ns=sX("Tablet",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["line",{x1:"12",x2:"12.01",y1:"18",y2:"18",key:"1dp563"}]]),nn=sX("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]),na=sX("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),no=sX("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),nl=sX("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]),nh=()=>(0,r.jsxs)("section",{className:"py-20 bg-gradient-to-br from-dark-800 to-dark-900 relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[r.jsx("div",{className:"absolute top-10 left-10 w-40 h-40 bg-blue-500 rounded-full blur-3xl"}),r.jsx("div",{className:"absolute bottom-10 right-10 w-40 h-40 bg-purple-500 rounded-full blur-3xl"})]}),(0,r.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:["Get ",r.jsx("span",{className:"gradient-text",children:"KRTR Mesh"})]}),r.jsx("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto",children:"Join the decentralized communication revolution. Download KRTR Mesh and start building resilient networks today."})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16",children:[{name:"iOS",icon:nr,description:"Native iOS app with full mesh networking capabilities",status:"Coming Soon",statusColor:"bg-yellow-500",link:"#",features:["Native Swift UI","Background mesh","Biometric unlock","AirDrop integration"]},{name:"Android",icon:ns,description:"Android app with Nearby Connections API integration",status:"In Development",statusColor:"bg-blue-500",link:"#",features:["Material Design","Background sync","Fingerprint auth","Quick share"]},{name:"macOS",icon:nn,description:"Desktop app for mesh coordination and development",status:"Beta Available",statusColor:"bg-green-500",link:"#",features:["Native macOS","Developer tools","Network analysis","Bridge mode"]}].map((e,t)=>r.jsx(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},className:"group",children:(0,r.jsxs)("div",{className:"bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center",children:r.jsx(e.icon,{className:"w-6 h-6 text-white"})}),r.jsx("h3",{className:"text-2xl font-bold text-white",children:e.name})]}),r.jsx("span",{className:`px-3 py-1 ${e.statusColor} text-white text-xs font-semibold rounded-full`,children:e.status})]}),r.jsx("p",{className:"text-gray-400 mb-6 leading-relaxed",children:e.description}),r.jsx("div",{className:"space-y-2 mb-8",children:e.features.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-1.5 h-1.5 bg-blue-400 rounded-full"}),r.jsx("span",{className:"text-sm text-gray-300",children:e})]},e))}),(0,r.jsxs)("button",{className:"w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 group-hover:scale-105",disabled:"Coming Soon"===e.status,children:[r.jsx(na,{className:"w-4 h-4"}),r.jsx("span",{children:"Coming Soon"===e.status?"Notify Me":"In Development"===e.status?"Join Beta":"Download"})]})]})},e.name))}),(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"bg-dark-800 border border-dark-700 rounded-2xl p-8 text-center",children:[r.jsx("div",{className:"flex justify-center mb-6",children:r.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center",children:r.jsx(no,{className:"w-8 h-8 text-white"})})}),r.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"Open Source & Transparent"}),r.jsx("p",{className:"text-gray-400 mb-8 max-w-2xl mx-auto",children:"KRTR Mesh is built in the open. Audit our code, contribute features, or build your own mesh applications using our protocols."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)("button",{className:"px-6 py-3 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300 flex items-center justify-center space-x-2",children:[r.jsx(no,{className:"w-4 h-4"}),r.jsx("span",{children:"View Source"})]}),(0,r.jsxs)("button",{className:"px-6 py-3 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300 flex items-center justify-center space-x-2",children:[r.jsx(nl,{className:"w-4 h-4"}),r.jsx("span",{children:"Documentation"})]})]})]}),r.jsx(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.5},viewport:{once:!0},className:"mt-16 text-center",children:(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-600/20 to-purple-600/20 border border-blue-500/30 rounded-2xl p-8",children:[r.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"Join the Beta Program"}),r.jsx("p",{className:"text-gray-300 mb-6 max-w-2xl mx-auto",children:"Get early access to KRTR Mesh, help shape the future of decentralized communication, and be part of the privacy revolution."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto",children:[r.jsx("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"}),r.jsx("button",{className:"px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300",children:"Join Beta"})]})]})})]})]}),nu=sX("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),nc=sX("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),nd=()=>r.jsx("footer",{className:"bg-dark-900 border-t border-dark-700",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-12",children:[r.jsx("div",{className:"lg:col-span-2",children:(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:r.jsx(sY,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("h3",{className:"text-2xl font-bold font-mono text-white",children:["krtr",r.jsx("span",{className:"text-blue-400",children:"*"}),"mesh"]})]}),r.jsx("p",{className:"text-gray-400 leading-relaxed max-w-sm",children:"Decentralized, encrypted, offline-first messaging for the post-platform era. Built from the blackout. Inspired by the streets."}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-green-400",children:[r.jsx(s_,{className:"w-4 h-4"}),r.jsx("span",{className:"text-xs",children:"Encrypted"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-yellow-400",children:[r.jsx(sJ,{className:"w-4 h-4"}),r.jsx("span",{className:"text-xs",children:"Zero-Knowledge"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-blue-400",children:[r.jsx(sY,{className:"w-4 h-4"}),r.jsx("span",{className:"text-xs",children:"Mesh Network"})]})]})]})}),Object.entries({Product:[{name:"Features",href:"#features"},{name:"Technology",href:"#tech"},{name:"Download",href:"#download"},{name:"Roadmap",href:"#roadmap"}],Developers:[{name:"Documentation",href:"#docs"},{name:"API Reference",href:"#api"},{name:"GitHub",href:"https://github.com/Z0rlord/krtr-mesh"},{name:"Contributing",href:"#contributing"}],Community:[{name:"Discord",href:"#discord"},{name:"Twitter",href:"#twitter"},{name:"Blog",href:"#blog"},{name:"Support",href:"#support"}],Legal:[{name:"Privacy Policy",href:"#privacy"},{name:"Terms of Service",href:"#terms"},{name:"Security",href:"#security"},{name:"License",href:"#license"}]}).map(([e,t],i)=>(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*i},viewport:{once:!0},className:"space-y-4",children:[r.jsx("h4",{className:"text-white font-semibold",children:e}),r.jsx("ul",{className:"space-y-2",children:t.map(e=>r.jsx("li",{children:r.jsx("a",{href:e.href,className:"text-gray-400 hover:text-white transition-colors text-sm",children:e.name})},e.name))})]},e))]}),r.jsx(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"bg-dark-800 border border-dark-700 rounded-2xl p-8 mb-12",children:(0,r.jsxs)("div",{className:"text-center max-w-2xl mx-auto",children:[r.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"Stay Updated"}),r.jsx("p",{className:"text-gray-400 mb-6",children:"Get the latest updates on KRTR Mesh development, security advisories, and community news delivered to your inbox."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[r.jsx("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"}),r.jsx("button",{className:"px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300",children:"Subscribe"})]})]})}),(0,r.jsxs)(sK.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.5},viewport:{once:!0},className:"flex flex-col md:flex-row justify-between items-center pt-8 border-t border-dark-700",children:[r.jsx("div",{className:"text-gray-400 text-sm mb-4 md:mb-0",children:"\xa9 2024 KRTR Team. Built for the people, by the people."}),r.jsx("div",{className:"flex space-x-6",children:[{icon:no,href:"https://github.com/Z0rlord/krtr-mesh",label:"GitHub"},{icon:nu,href:"#twitter",label:"Twitter"},{icon:nc,href:"mailto:<EMAIL>",label:"Email"}].map(e=>r.jsx("a",{href:e.href,className:"text-gray-400 hover:text-white transition-colors","aria-label":e.label,children:r.jsx(e.icon,{className:"w-5 h-5"})},e.label))})]}),(0,r.jsxs)(sK.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.8,delay:.7},viewport:{once:!0},className:"text-center mt-8",children:[r.jsx("p",{className:"text-gray-500 text-sm font-mono",children:"\uD83D\uDD73️ We build from the edge."}),r.jsx("p",{className:"text-gray-600 text-xs mt-2 italic",children:'"Decentralized by design. Privacy by default. Built for the people."'})]})]})});function np(){return(0,r.jsxs)("main",{className:"min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900",children:[r.jsx(s0,{}),r.jsx(s4,{}),r.jsx(ne,{}),r.jsx(ni,{}),r.jsx(nh,{}),r.jsx(nd,{})]})}},2029:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>h,metadata:()=>l});var r=i(9510),s=i(5036),n=i.n(s),a=i(909),o=i.n(a);i(5023);let l={title:"KRTR Mesh - Decentralized Encrypted Messaging",description:"Decentralized, encrypted, offline-first messaging for the post-platform era. Built from the blackout. Inspired by the streets.",keywords:["mesh networking","decentralized messaging","privacy","encryption","zero-knowledge","offline-first","bluetooth","peer-to-peer"],authors:[{name:"KRTR Team"}],creator:"KRTR Team",publisher:"KRTR Team",openGraph:{title:"KRTR Mesh - Decentralized Encrypted Messaging",description:"Decentralized, encrypted, offline-first messaging for the post-platform era.",url:"https://krtr-mesh.vercel.app",siteName:"KRTR Mesh",images:[{url:"/og-image.png",width:1200,height:630,alt:"KRTR Mesh - Decentralized Messaging"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"KRTR Mesh - Decentralized Encrypted Messaging",description:"Decentralized, encrypted, offline-first messaging for the post-platform era.",images:["/og-image.png"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function h({children:e}){return r.jsx("html",{lang:"en",className:`${n().variable} ${o().variable}`,children:r.jsx("body",{className:`${n().className} antialiased`,children:e})})}},5480:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r});let r=(0,i(8570).createProxy)(String.raw`/Users/<USER>/krtr-mesh-local/landing-page/src/app/page.tsx#default`)},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[499],()=>i(2566));module.exports=r})();