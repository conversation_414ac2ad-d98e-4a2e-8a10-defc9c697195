(()=>{var e={};e.id=409,e.ids=[409],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},6482:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>f,tree:()=>d}),r(7352),r(5866),r(2029);var n=r(3191),o=r(8716),i=r(7922),s=r.n(i),a=r(5231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"/Users/<USER>/krtr-mesh-local/landing-page/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],u=[],c="/_not-found/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1968:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},6581:()=>{},6399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7352:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PARALLEL_ROUTE_DEFAULT_PATH:function(){return o},default:function(){return i}});let n=r(6399),o="next/dist/client/components/parallel-route-default.js";function i(){(0,n.notFound)()}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var n=r(9510),o=r(5036),i=r.n(o),s=r(909),a=r.n(s);r(5023);let l={title:"KRTR Mesh - Decentralized Encrypted Messaging",description:"Decentralized, encrypted, offline-first messaging for the post-platform era. Built from the blackout. Inspired by the streets.",keywords:["mesh networking","decentralized messaging","privacy","encryption","zero-knowledge","offline-first","bluetooth","peer-to-peer"],authors:[{name:"KRTR Team"}],creator:"KRTR Team",publisher:"KRTR Team",openGraph:{title:"KRTR Mesh - Decentralized Encrypted Messaging",description:"Decentralized, encrypted, offline-first messaging for the post-platform era.",url:"https://krtr-mesh.vercel.app",siteName:"KRTR Mesh",images:[{url:"/og-image.png",width:1200,height:630,alt:"KRTR Mesh - Decentralized Messaging"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"KRTR Mesh - Decentralized Encrypted Messaging",description:"Decentralized, encrypted, offline-first messaging for the post-platform era.",images:["/og-image.png"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function d({children:e}){return n.jsx("html",{lang:"en",className:`${i().variable} ${a().variable}`,children:n.jsx("body",{className:`${i().className} antialiased`,children:e})})}},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[499],()=>r(6482));module.exports=n})();