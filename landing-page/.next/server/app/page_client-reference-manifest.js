globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"6":{"*":{"id":"3263","name":"*","chunks":[],"async":false}},"80":{"*":{"id":"1868","name":"*","chunks":[],"async":false}},"1060":{"*":{"id":"9727","name":"*","chunks":[],"async":false}},"2846":{"*":{"id":"2994","name":"*","chunks":[],"async":false}},"4707":{"*":{"id":"9671","name":"*","chunks":[],"async":false}},"6423":{"*":{"id":"4759","name":"*","chunks":[],"async":false}},"9107":{"*":{"id":"6114","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/client/components/app-router.js":{"id":2846,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/esm/client/components/app-router.js":{"id":2846,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/client/components/client-page.js":{"id":9107,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/esm/client/components/client-page.js":{"id":9107,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/client/components/error-boundary.js":{"id":1060,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":1060,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/client/components/layout-router.js":{"id":4707,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/esm/client/components/layout-router.js":{"id":4707,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/client/components/not-found-boundary.js":{"id":80,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":80,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/client/components/render-from-template-context.js":{"id":6423,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":6423,"name":"*","chunks":[],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":2827,"name":"*","chunks":["185","static/chunks/app/layout-dbb175f3dea2b548.js"],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-mono\"}],\"variableName\":\"jetbrainsMono\"}":{"id":884,"name":"*","chunks":["185","static/chunks/app/layout-dbb175f3dea2b548.js"],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/src/app/globals.css":{"id":2778,"name":"*","chunks":["185","static/chunks/app/layout-dbb175f3dea2b548.js"],"async":false},"/Users/<USER>/krtr-mesh-local/landing-page/src/app/page.tsx":{"id":6,"name":"*","chunks":["755","static/chunks/755-1b84a57102143abe.js","931","static/chunks/app/page-10fdf8e5ef664973.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/krtr-mesh-local/landing-page/src/":[],"/Users/<USER>/krtr-mesh-local/landing-page/src/app/layout":["static/css/ee2406ec3f3397fb.css"],"/Users/<USER>/krtr-mesh-local/landing-page/src/app/page":[]}}