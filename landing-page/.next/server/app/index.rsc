2:I[9107,[],"ClientPageRoot"]
3:I[6,["755","static/chunks/755-1b84a57102143abe.js","931","static/chunks/app/page-10fdf8e5ef664973.js"],"default",1]
4:I[4707,[],""]
5:I[6423,[],""]
0:["DrxacXKQcZU5y4b-qQisL",[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",{"children":["__PAGE__",{},[["$L1",["$","$L2",null,{"props":{"params":{},"searchParams":{}},"Component":"$3"}],null],null],null]},[[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/ee2406ec3f3397fb.css","precedence":"next","crossOrigin":"$undefined"}]],["$","html",null,{"lang":"en","className":"__variable_e8ce0c __variable_3c557b","children":["$","body",null,{"className":"__className_e8ce0c antialiased","children":["$","$L4",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[]}]}]}]],null],null],["$L6",null]]]]
6:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"KRTR Mesh - Decentralized Encrypted Messaging"}],["$","meta","3",{"name":"description","content":"Decentralized, encrypted, offline-first messaging for the post-platform era. Built from the blackout. Inspired by the streets."}],["$","meta","4",{"name":"author","content":"KRTR Team"}],["$","meta","5",{"name":"keywords","content":"mesh networking,decentralized messaging,privacy,encryption,zero-knowledge,offline-first,bluetooth,peer-to-peer"}],["$","meta","6",{"name":"creator","content":"KRTR Team"}],["$","meta","7",{"name":"publisher","content":"KRTR Team"}],["$","meta","8",{"name":"robots","content":"index, follow"}],["$","meta","9",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","10",{"property":"og:title","content":"KRTR Mesh - Decentralized Encrypted Messaging"}],["$","meta","11",{"property":"og:description","content":"Decentralized, encrypted, offline-first messaging for the post-platform era."}],["$","meta","12",{"property":"og:url","content":"https://krtr-mesh.vercel.app/"}],["$","meta","13",{"property":"og:site_name","content":"KRTR Mesh"}],["$","meta","14",{"property":"og:locale","content":"en_US"}],["$","meta","15",{"property":"og:image","content":"http://localhost:3000/og-image.png"}],["$","meta","16",{"property":"og:image:width","content":"1200"}],["$","meta","17",{"property":"og:image:height","content":"630"}],["$","meta","18",{"property":"og:image:alt","content":"KRTR Mesh - Decentralized Messaging"}],["$","meta","19",{"property":"og:type","content":"website"}],["$","meta","20",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","21",{"name":"twitter:title","content":"KRTR Mesh - Decentralized Encrypted Messaging"}],["$","meta","22",{"name":"twitter:description","content":"Decentralized, encrypted, offline-first messaging for the post-platform era."}],["$","meta","23",{"name":"twitter:image","content":"http://localhost:3000/og-image.png"}],["$","meta","24",{"name":"next-size-adjust"}]]
1:null
