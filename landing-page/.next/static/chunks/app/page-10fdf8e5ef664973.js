(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{5625:function(e,t,i){Promise.resolve().then(i.bind(i,6))},6:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return I}});var a=i(7437),s=i(3287),r=i(2926),n=i(8906),l=i(1239),o=i(5805),c=()=>(0,a.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,a.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 1000 1000",children:[(0,a.jsx)("defs",{children:(0,a.jsx)("pattern",{id:"mesh",x:"0",y:"0",width:"100",height:"100",patternUnits:"userSpaceOnUse",children:(0,a.jsx)("circle",{cx:"50",cy:"50",r:"2",fill:"currentColor",className:"text-blue-400"})})}),(0,a.jsx)("rect",{width:"100%",height:"100%",fill:"url(#mesh)"}),(0,a.jsx)(s.E.line,{x1:"100",y1:"100",x2:"300",y2:"200",stroke:"currentColor",strokeWidth:"1",className:"text-blue-400 mesh-connect",initial:{pathLength:0},animate:{pathLength:1},transition:{duration:2,repeat:1/0,repeatType:"reverse"}}),(0,a.jsx)(s.E.line,{x1:"300",y1:"200",x2:"500",y2:"150",stroke:"currentColor",strokeWidth:"1",className:"text-blue-400 mesh-connect",initial:{pathLength:0},animate:{pathLength:1},transition:{duration:2,delay:.5,repeat:1/0,repeatType:"reverse"}}),(0,a.jsx)(s.E.line,{x1:"500",y1:"150",x2:"700",y2:"300",stroke:"currentColor",strokeWidth:"1",className:"text-blue-400 mesh-connect",initial:{pathLength:0},animate:{pathLength:1},transition:{duration:2,delay:1,repeat:1/0,repeatType:"reverse"}})]})}),(0,a.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-8",children:[(0,a.jsxs)(s.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.8,delay:.2},className:"flex items-center justify-center space-x-3",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center",children:(0,a.jsx)(r.Z,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"})]}),(0,a.jsxs)("h1",{className:"text-5xl md:text-7xl font-bold font-mono text-white",children:["krtr",(0,a.jsx)("span",{className:"text-blue-400",children:"*"}),"mesh"]})]}),(0,a.jsxs)(s.E.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed",children:[(0,a.jsx)("span",{className:"gradient-text font-semibold",children:"Decentralized, encrypted, offline-first messaging"}),(0,a.jsx)("br",{}),"for the post-platform era."]}),(0,a.jsx)(s.E.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"text-lg text-gray-400 max-w-2xl mx-auto font-mono",children:"Built from the blackout. Inspired by the streets. Whispered across devices."}),(0,a.jsx)(s.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"flex flex-wrap justify-center gap-8 mt-12",children:[{icon:n.Z,label:"End-to-End Encrypted",color:"text-green-400"},{icon:l.Z,label:"Zero-Knowledge Proofs",color:"text-yellow-400"},{icon:o.Z,label:"Mesh Networking",color:"text-blue-400"},{icon:r.Z,label:"Offline-First",color:"text-purple-400"}].map((e,t)=>(0,a.jsxs)(s.E.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1+.1*t},className:"flex flex-col items-center space-y-2 group cursor-pointer",children:[(0,a.jsx)("div",{className:"p-4 rounded-xl bg-dark-800 border border-dark-700 group-hover:border-blue-500 transition-colors ".concat(e.color),children:(0,a.jsx)(e.icon,{className:"w-6 h-6"})}),(0,a.jsx)("span",{className:"text-sm text-gray-400 group-hover:text-white transition-colors",children:e.label})]},e.label))}),(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1.2},className:"flex flex-col sm:flex-row gap-4 justify-center mt-12",children:[(0,a.jsx)("button",{className:"px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg",children:"Download App"}),(0,a.jsx)("button",{className:"px-8 py-4 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300",children:"Learn More"})]}),(0,a.jsx)(s.E.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:1.5},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,a.jsx)(s.E.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-gray-600 rounded-full flex justify-center",children:(0,a.jsx)(s.E.div,{animate:{y:[0,12,0]},transition:{duration:2,repeat:1/0},className:"w-1 h-3 bg-blue-400 rounded-full mt-2"})})})]})})]}),d=i(3420),m=i(2718),x=i(2208),h=i(5673),p=()=>{let e=[{icon:d.Z,title:"Mesh Networking",description:"Multi-hop routing with TTL-based forwarding. Connect up to 20 peers simultaneously with automatic discovery via Bluetooth LE.",details:["Automatic peer discovery","Multi-hop routing (max 7 hops)","Connection management","Loop prevention"],color:"from-blue-500 to-cyan-500"},{icon:n.Z,title:"End-to-End Encryption",description:"Military-grade encryption with X25519 key exchange, AES-256-GCM, and Ed25519 signatures for message authenticity.",details:["X25519 key exchange","AES-256-GCM encryption","Ed25519 signatures","Forward secrecy"],color:"from-green-500 to-emerald-500"},{icon:l.Z,title:"Zero-Knowledge Proofs",description:"Anonymous authentication and private reputation using Noir ZK circuits. Prove membership without revealing identity.",details:["Anonymous authentication","Private reputation","Selective disclosure","Group authorization"],color:"from-yellow-500 to-orange-500"},{icon:m.Z,title:"Store-and-Forward",description:"Offline message delivery with intelligent routing. Messages reach their destination even when peers are temporarily offline.",details:["Offline message delivery","Message persistence","Intelligent routing","Delivery confirmation"],color:"from-purple-500 to-pink-500"},{icon:x.Z,title:"Privacy Features",description:"Cover traffic generation and timing randomization prevent traffic analysis. Emergency wipe capability for security.",details:["Cover traffic generation","Timing randomization","Ephemeral identities","Emergency wipe"],color:"from-indigo-500 to-purple-500"},{icon:h.Z,title:"Battery Optimization",description:"4-tier power management with adaptive connection limits. LZ4 compression saves 30-70% bandwidth.",details:["4-tier power management","Adaptive connections","LZ4 compression","Binary protocol"],color:"from-red-500 to-pink-500"}];return(0,a.jsxs)("section",{className:"py-20 bg-dark-900 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-5",children:(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"})}),(0,a.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:["Built for ",(0,a.jsx)("span",{className:"gradient-text",children:"Privacy"})," & ",(0,a.jsx)("span",{className:"gradient-text",children:"Performance"})]}),(0,a.jsx)("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto",children:"KRTR Mesh combines cutting-edge cryptography with mesh networking to deliver secure, private, and resilient communication that works anywhere."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map((e,t)=>(0,a.jsx)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},className:"group",children:(0,a.jsxs)("div",{className:"bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-xl bg-gradient-to-r ".concat(e.color," p-4 mb-6 group-hover:scale-110 transition-transform duration-300"),children:(0,a.jsx)(e.icon,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors",children:e.title}),(0,a.jsx)("p",{className:"text-gray-400 mb-6 leading-relaxed",children:e.description}),(0,a.jsx)("ul",{className:"space-y-2",children:e.details.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center text-sm text-gray-500",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"}),e]},t))})]})},e.title))}),(0,a.jsx)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.5},viewport:{once:!0},className:"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8",children:[{value:"7",label:"Max Hops",suffix:""},{value:"20",label:"Max Connections",suffix:""},{value:"70",label:"Bandwidth Savings",suffix:"%"},{value:"256",label:"AES Encryption",suffix:"-bit"}].map((e,t)=>(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-3xl md:text-4xl font-bold text-white mb-2",children:[e.value,(0,a.jsx)("span",{className:"text-blue-400",children:e.suffix})]}),(0,a.jsx)("div",{className:"text-gray-400 text-sm",children:e.label})]},e.label))})]})]})},u=i(8124),y=i(4822),g=i(4935),b=i(91),f=()=>{let e=[{category:"Networking",icon:u.Z,color:"from-blue-500 to-cyan-500",technologies:[{name:"Bluetooth LE Mesh",description:"Low-energy mesh networking"},{name:"Multi-hop Routing",description:"TTL-based message forwarding"},{name:"Store-and-Forward",description:"Offline message delivery"},{name:"Connection Management",description:"Adaptive peer limits"}]},{category:"Cryptography",icon:n.Z,color:"from-green-500 to-emerald-500",technologies:[{name:"X25519 ECDH",description:"Key exchange with forward secrecy"},{name:"AES-256-GCM",description:"Authenticated encryption"},{name:"Ed25519",description:"Digital signatures"},{name:"Argon2id",description:"Password-based key derivation"}]},{category:"Zero-Knowledge",icon:l.Z,color:"from-yellow-500 to-orange-500",technologies:[{name:"Noir Circuits",description:"ZK proof generation"},{name:"Anonymous Auth",description:"Identity-preserving verification"},{name:"Private Reputation",description:"Trust without exposure"},{name:"Selective Disclosure",description:"Prove without revealing"}]},{category:"Performance",icon:y.Z,color:"from-purple-500 to-pink-500",technologies:[{name:"LZ4 Compression",description:"30-70% bandwidth savings"},{name:"Binary Protocol",description:"Efficient message encoding"},{name:"Battery Optimization",description:"4-tier power management"},{name:"Message Fragmentation",description:"Large payload handling"}]}];return(0,a.jsxs)("section",{className:"py-20 bg-gradient-to-br from-dark-800 to-dark-900 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,a.jsx)("div",{className:"absolute top-20 left-20 w-32 h-32 bg-blue-500 rounded-full blur-3xl"}),(0,a.jsx)("div",{className:"absolute bottom-20 right-20 w-32 h-32 bg-purple-500 rounded-full blur-3xl"})]}),(0,a.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:[(0,a.jsx)("span",{className:"gradient-text",children:"Technical"})," Architecture"]}),(0,a.jsx)("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto",children:"Built on proven cryptographic primitives and modern networking protocols, KRTR Mesh delivers enterprise-grade security with consumer-friendly usability."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-20",children:e.map((e,t)=>(0,a.jsxs)(s.E.div,{initial:{opacity:0,x:t%2==0?-20:20},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},className:"bg-dark-800 border border-dark-700 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-xl bg-gradient-to-r ".concat(e.color," p-3 mr-4"),children:(0,a.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white",children:e.category})]}),(0,a.jsx)("div",{className:"space-y-4",children:e.technologies.map((e,t)=>(0,a.jsx)("div",{className:"flex justify-between items-start",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-white font-semibold",children:e.name}),(0,a.jsx)("div",{className:"text-gray-400 text-sm",children:e.description})]})},e.name))})]},e.category))}),(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"bg-dark-800 border border-dark-700 rounded-2xl p-8",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-8 text-center",children:"System Architecture"}),(0,a.jsx)("div",{className:"space-y-4",children:[{name:"Application Layer",description:"SwiftUI / React Native UI",color:"bg-blue-500"},{name:"Service Layer",description:"Mesh, Crypto, ZK Services",color:"bg-green-500"},{name:"Protocol Layer",description:"KRTR Binary Protocol",color:"bg-yellow-500"},{name:"Transport Layer",description:"Bluetooth LE Abstraction",color:"bg-purple-500"},{name:"Hardware Layer",description:"iOS / Android / macOS",color:"bg-red-500"}].map((e,t)=>(0,a.jsxs)(s.E.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5,delay:.1*t},viewport:{once:!0},className:"flex items-center p-4 bg-dark-700 rounded-xl hover:bg-dark-600 transition-colors",children:[(0,a.jsx)("div",{className:"w-4 h-4 ".concat(e.color," rounded-full mr-4")}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"text-white font-semibold",children:e.name}),(0,a.jsx)("div",{className:"text-gray-400 text-sm",children:e.description})]}),(0,a.jsx)(g.Z,{className:"w-5 h-5 text-gray-500"})]},e.name))})]}),(0,a.jsx)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8",children:[{title:"Forward Secrecy",description:"New keys generated each session ensure past communications remain secure even if current keys are compromised.",icon:n.Z},{title:"Zero Trust",description:"No central servers or authorities. Every message is cryptographically verified and authenticated.",icon:b.Z},{title:"Privacy by Design",description:"Cover traffic, timing randomization, and ephemeral identities prevent traffic analysis.",icon:l.Z}].map((e,t)=>(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(e.icon,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h4",{className:"text-xl font-bold text-white mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-gray-400 text-sm leading-relaxed",children:e.description})]},e.title))})]})]})},v=i(6221),w=()=>{let e=[{title:"Dashboard",description:"Real-time network status, connection metrics, and quick actions all in one place.",icon:v.Z,features:["Network Status","Connected Peers","Battery Level","Power Mode"],mockup:{header:"Dashboard",content:[{label:"Network Status",value:"Active",status:"online"},{label:"Connected Peers",value:"7",status:"normal"},{label:"Battery Level",value:"85%",status:"good"},{label:"Power Mode",value:"Balanced",status:"normal"}]}},{title:"Mesh Network",description:"Visualize your mesh connections, peer discovery, and network topology in real-time.",icon:d.Z,features:["Peer Discovery","Connection Map","Signal Strength","Routing Info"],mockup:{header:"Mesh Network",content:[{label:"alice@mesh",value:"Connected",status:"online"},{label:"bob@mesh",value:"Discovering",status:"pending"},{label:"charlie@mesh",value:"Connected",status:"online"},{label:"diana@mesh",value:"Offline",status:"offline"}]}},{title:"Secure Chat",description:"End-to-end encrypted messaging with forward secrecy and anonymous authentication.",icon:m.Z,features:["E2E Encryption","Group Chats","File Sharing","Message Status"],mockup:{header:"General Chat",content:[{label:"alice",value:"Hey everyone! \uD83D\uDC4B",status:"received"},{label:"bob",value:"Network is looking good today",status:"received"},{label:"You",value:"Agreed! Zero issues so far",status:"sent"},{label:"charlie",value:"Love the new ZK features",status:"received"}]}},{title:"ZK Features",description:"Zero-knowledge proofs for anonymous authentication and private reputation systems.",icon:l.Z,features:["Anonymous Auth","Private Reputation","Proof Generation","Verification"],mockup:{header:"ZK Dashboard",content:[{label:"Identity Proof",value:"Generated",status:"online"},{label:"Reputation Score",value:"Hidden",status:"private"},{label:"Group Membership",value:"Verified",status:"online"},{label:"Anonymous Mode",value:"Active",status:"private"}]}}];return(0,a.jsxs)("section",{className:"py-20 bg-dark-900 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-900/10 to-purple-900/10"}),(0,a.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:["Experience ",(0,a.jsx)("span",{className:"gradient-text",children:"KRTR Mesh"})]}),(0,a.jsx)("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto",children:"Simple, intuitive interfaces that put powerful mesh networking and cryptographic privacy at your fingertips."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:e.map((e,t)=>(0,a.jsx)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},className:"group",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row items-center gap-8",children:[(0,a.jsxs)("div",{className:"flex-1 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center",children:(0,a.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white",children:e.title})]}),(0,a.jsx)("p",{className:"text-gray-400 leading-relaxed",children:e.description}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-3",children:e.features.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:e})]},e))})]}),(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-64 h-96 bg-dark-800 rounded-3xl border-4 border-dark-700 p-4 group-hover:border-blue-500/50 transition-all duration-300",children:(0,a.jsxs)("div",{className:"bg-dark-700 rounded-2xl h-full p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center text-xs text-gray-400",children:[(0,a.jsx)("span",{children:"9:41"}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-4 h-2 bg-green-400 rounded-sm"}),(0,a.jsx)("div",{className:"w-4 h-2 bg-blue-400 rounded-sm"}),(0,a.jsx)("div",{className:"w-4 h-2 bg-gray-600 rounded-sm"})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("h4",{className:"text-white font-semibold",children:e.mockup.header})}),(0,a.jsx)("div",{className:"space-y-3 flex-1",children:e.mockup.content.map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between items-center p-2 bg-dark-600 rounded-lg",children:[(0,a.jsx)("span",{className:"text-gray-300 text-sm",children:e.label}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-white text-sm",children:e.value}),(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat("online"===e.status?"bg-green-400":"offline"===e.status?"bg-red-400":"pending"===e.status?"bg-yellow-400":"private"===e.status?"bg-purple-400":"sent"===e.status?"bg-blue-400":"received"===e.status?"bg-gray-400":"bg-gray-500")})]})]},t))}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"w-8 h-1 bg-gray-600 rounded-full"})})]})})})]})},e.title))}),(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.5},viewport:{once:!0},className:"mt-20 text-center",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-8",children:"Available on All Platforms"}),(0,a.jsx)("div",{className:"flex justify-center space-x-8",children:[{name:"iOS",icon:"\uD83D\uDCF1"},{name:"Android",icon:"\uD83E\uDD16"},{name:"macOS",icon:"\uD83D\uDCBB"}].map(e=>(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)("div",{className:"text-4xl",children:e.icon}),(0,a.jsx)("span",{className:"text-gray-400",children:e.name})]},e.name))})]})]})]})},j=i(3388),N=i(8553),k=i(9897),E=i(2735),Z=i(5135),D=i(6362),S=()=>{let e=[{name:"iOS",icon:j.Z,description:"Native iOS app with full mesh networking capabilities",status:"Coming Soon",statusColor:"bg-yellow-500",link:"#",features:["Native Swift UI","Background mesh","Biometric unlock","AirDrop integration"]},{name:"Android",icon:N.Z,description:"Android app with Nearby Connections API integration",status:"In Development",statusColor:"bg-blue-500",link:"#",features:["Material Design","Background sync","Fingerprint auth","Quick share"]},{name:"macOS",icon:k.Z,description:"Desktop app for mesh coordination and development",status:"Beta Available",statusColor:"bg-green-500",link:"#",features:["Native macOS","Developer tools","Network analysis","Bridge mode"]}];return(0,a.jsxs)("section",{className:"py-20 bg-gradient-to-br from-dark-800 to-dark-900 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,a.jsx)("div",{className:"absolute top-10 left-10 w-40 h-40 bg-blue-500 rounded-full blur-3xl"}),(0,a.jsx)("div",{className:"absolute bottom-10 right-10 w-40 h-40 bg-purple-500 rounded-full blur-3xl"})]}),(0,a.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:["Get ",(0,a.jsx)("span",{className:"gradient-text",children:"KRTR Mesh"})]}),(0,a.jsx)("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto",children:"Join the decentralized communication revolution. Download KRTR Mesh and start building resilient networks today."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16",children:e.map((e,t)=>(0,a.jsx)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},className:"group",children:(0,a.jsxs)("div",{className:"bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center",children:(0,a.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white",children:e.name})]}),(0,a.jsx)("span",{className:"px-3 py-1 ".concat(e.statusColor," text-white text-xs font-semibold rounded-full"),children:e.status})]}),(0,a.jsx)("p",{className:"text-gray-400 mb-6 leading-relaxed",children:e.description}),(0,a.jsx)("div",{className:"space-y-2 mb-8",children:e.features.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-blue-400 rounded-full"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:e})]},e))}),(0,a.jsxs)("button",{className:"w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 group-hover:scale-105",disabled:"Coming Soon"===e.status,children:[(0,a.jsx)(E.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Coming Soon"===e.status?"Notify Me":"In Development"===e.status?"Join Beta":"Download"})]})]})},e.name))}),(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"bg-dark-800 border border-dark-700 rounded-2xl p-8 text-center",children:[(0,a.jsx)("div",{className:"flex justify-center mb-6",children:(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center",children:(0,a.jsx)(Z.Z,{className:"w-8 h-8 text-white"})})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Open Source & Transparent"}),(0,a.jsx)("p",{className:"text-gray-400 mb-8 max-w-2xl mx-auto",children:"KRTR Mesh is built in the open. Audit our code, contribute features, or build your own mesh applications using our protocols."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)("button",{className:"px-6 py-3 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300 flex items-center justify-center space-x-2",children:[(0,a.jsx)(Z.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"View Source"})]}),(0,a.jsxs)("button",{className:"px-6 py-3 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300 flex items-center justify-center space-x-2",children:[(0,a.jsx)(D.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Documentation"})]})]})]}),(0,a.jsx)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.5},viewport:{once:!0},className:"mt-16 text-center",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-600/20 to-purple-600/20 border border-blue-500/30 rounded-2xl p-8",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Join the Beta Program"}),(0,a.jsx)("p",{className:"text-gray-300 mb-6 max-w-2xl mx-auto",children:"Get early access to KRTR Mesh, help shape the future of decentralized communication, and be part of the privacy revolution."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto",children:[(0,a.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"}),(0,a.jsx)("button",{className:"px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300",children:"Join Beta"})]})]})})]})]})},C=i(2351),M=i(9345),A=()=>(0,a.jsx)("footer",{className:"bg-dark-900 border-t border-dark-700",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-12",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(r.Z,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("h3",{className:"text-2xl font-bold font-mono text-white",children:["krtr",(0,a.jsx)("span",{className:"text-blue-400",children:"*"}),"mesh"]})]}),(0,a.jsx)("p",{className:"text-gray-400 leading-relaxed max-w-sm",children:"Decentralized, encrypted, offline-first messaging for the post-platform era. Built from the blackout. Inspired by the streets."}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-green-400",children:[(0,a.jsx)(n.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-xs",children:"Encrypted"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-yellow-400",children:[(0,a.jsx)(l.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-xs",children:"Zero-Knowledge"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-blue-400",children:[(0,a.jsx)(r.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-xs",children:"Mesh Network"})]})]})]})}),Object.entries({Product:[{name:"Features",href:"#features"},{name:"Technology",href:"#tech"},{name:"Download",href:"#download"},{name:"Roadmap",href:"#roadmap"}],Developers:[{name:"Documentation",href:"#docs"},{name:"API Reference",href:"#api"},{name:"GitHub",href:"https://github.com/Z0rlord/krtr-mesh"},{name:"Contributing",href:"#contributing"}],Community:[{name:"Discord",href:"#discord"},{name:"Twitter",href:"#twitter"},{name:"Blog",href:"#blog"},{name:"Support",href:"#support"}],Legal:[{name:"Privacy Policy",href:"#privacy"},{name:"Terms of Service",href:"#terms"},{name:"Security",href:"#security"},{name:"License",href:"#license"}]}).map((e,t)=>{let[i,r]=e;return(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:i}),(0,a.jsx)("ul",{className:"space-y-2",children:r.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-white transition-colors text-sm",children:e.name})},e.name))})]},i)})]}),(0,a.jsx)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"bg-dark-800 border border-dark-700 rounded-2xl p-8 mb-12",children:(0,a.jsxs)("div",{className:"text-center max-w-2xl mx-auto",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Stay Updated"}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:"Get the latest updates on KRTR Mesh development, security advisories, and community news delivered to your inbox."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[(0,a.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"}),(0,a.jsx)("button",{className:"px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300",children:"Subscribe"})]})]})}),(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.5},viewport:{once:!0},className:"flex flex-col md:flex-row justify-between items-center pt-8 border-t border-dark-700",children:[(0,a.jsx)("div",{className:"text-gray-400 text-sm mb-4 md:mb-0",children:"\xa9 2024 KRTR Team. Built for the people, by the people."}),(0,a.jsx)("div",{className:"flex space-x-6",children:[{icon:Z.Z,href:"https://github.com/Z0rlord/krtr-mesh",label:"GitHub"},{icon:C.Z,href:"#twitter",label:"Twitter"},{icon:M.Z,href:"mailto:<EMAIL>",label:"Email"}].map(e=>(0,a.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-white transition-colors","aria-label":e.label,children:(0,a.jsx)(e.icon,{className:"w-5 h-5"})},e.label))})]}),(0,a.jsxs)(s.E.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.8,delay:.7},viewport:{once:!0},className:"text-center mt-8",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm font-mono",children:"\uD83D\uDD73️ We build from the edge."}),(0,a.jsx)("p",{className:"text-gray-600 text-xs mt-2 italic",children:'"Decentralized by design. Privacy by default. Built for the people."'})]})]})});function I(){return(0,a.jsxs)("main",{className:"min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900",children:[(0,a.jsx)(c,{}),(0,a.jsx)(p,{}),(0,a.jsx)(f,{}),(0,a.jsx)(w,{}),(0,a.jsx)(S,{}),(0,a.jsx)(A,{})]})}}},function(e){e.O(0,[755,971,117,744],function(){return e(e.s=5625)}),_N_E=e.O()}]);