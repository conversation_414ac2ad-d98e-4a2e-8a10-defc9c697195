# KRTR Mesh Landing Page

A modern, responsive landing page for KRTR Mesh - the decentralized, encrypted, offline-first messaging application.

## 🚀 Features

- **Modern Design**: Clean, professional design with dark theme
- **Responsive**: Fully responsive design that works on all devices
- **Animations**: Smooth animations and transitions using Framer Motion
- **Performance**: Optimized for fast loading and smooth interactions
- **SEO Optimized**: Proper meta tags and structured data
- **Accessibility**: Built with accessibility best practices

## 🛠️ Tech Stack

- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **Lucide React** - Beautiful icons

## 📦 Installation

1. Navigate to the landing page directory:
```bash
cd landing-page
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Build & Deploy

### Local Build
```bash
npm run build
npm start
```

### Deploy to Vercel
This project is configured for automatic deployment to Vercel via GitHub Actions.

1. Connect your GitHub repository to Vercel
2. Push to the main branch
3. Vercel will automatically deploy your changes

## 📁 Project Structure

```
landing-page/
├── src/
│   ├── app/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   └── components/
│       ├── HeroSection.tsx
│       ├── FeaturesSection.tsx
│       ├── TechSection.tsx
│       ├── AppShowcase.tsx
│       ├── DownloadSection.tsx
│       └── Footer.tsx
├── public/
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
└── package.json
```

## 🎨 Customization

### Colors
The color scheme is defined in `tailwind.config.js`. Main colors:
- Primary: Blue gradient (#3b82f6 to #2563eb)
- Secondary: Purple gradient (#8b5cf6 to #7c3aed)
- Dark theme: Various shades of gray/slate

### Content
Update the content in each component file:
- `HeroSection.tsx` - Main hero content and taglines
- `FeaturesSection.tsx` - Feature descriptions and details
- `TechSection.tsx` - Technical specifications
- `AppShowcase.tsx` - App screenshots and interface mockups
- `DownloadSection.tsx` - Download links and platform availability
- `Footer.tsx` - Footer links and contact information

### Animations
Animations are handled by Framer Motion. Key animation patterns:
- Fade in on scroll
- Slide up animations
- Hover effects
- Stagger animations for lists

## 🔧 Configuration

### Next.js Config
- Static export enabled for Vercel deployment
- Image optimization disabled for static export
- Trailing slashes enabled

### Tailwind Config
- Custom color palette
- Extended animations
- Custom font families
- Responsive breakpoints

## 📱 Responsive Design

The landing page is fully responsive with breakpoints:
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

## 🚀 Performance

- Static site generation for fast loading
- Optimized images and assets
- Minimal JavaScript bundle
- Efficient CSS with Tailwind's purging

## 📄 License

MIT License - see the main project LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For questions or support, please open an issue in the main KRTR Mesh repository.
