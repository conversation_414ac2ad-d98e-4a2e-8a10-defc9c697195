<!DOCTYPE html><html lang="en" class="__variable_e8ce0c __variable_3c557b"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/bb3ef058b751a6ad-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/ee2406ec3f3397fb.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-0f866bb3c58b1f53.js"/><script src="/_next/static/chunks/fd9d1056-fee79df7e2a8395b.js" async=""></script><script src="/_next/static/chunks/117-6b1ce19666c6dbcf.js" async=""></script><script src="/_next/static/chunks/main-app-a2feaff55b3309f3.js" async=""></script><script src="/_next/static/chunks/755-1b84a57102143abe.js" async=""></script><script src="/_next/static/chunks/app/page-10fdf8e5ef664973.js" async=""></script><title>KRTR Mesh - Decentralized Encrypted Messaging</title><meta name="description" content="Decentralized, encrypted, offline-first messaging for the post-platform era. Built from the blackout. Inspired by the streets."/><meta name="author" content="KRTR Team"/><meta name="keywords" content="mesh networking,decentralized messaging,privacy,encryption,zero-knowledge,offline-first,bluetooth,peer-to-peer"/><meta name="creator" content="KRTR Team"/><meta name="publisher" content="KRTR Team"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta property="og:title" content="KRTR Mesh - Decentralized Encrypted Messaging"/><meta property="og:description" content="Decentralized, encrypted, offline-first messaging for the post-platform era."/><meta property="og:url" content="https://krtr-mesh.vercel.app/"/><meta property="og:site_name" content="KRTR Mesh"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="http://localhost:3000/og-image.png"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="KRTR Mesh - Decentralized Messaging"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="KRTR Mesh - Decentralized Encrypted Messaging"/><meta name="twitter:description" content="Decentralized, encrypted, offline-first messaging for the post-platform era."/><meta name="twitter:image" content="http://localhost:3000/og-image.png"/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c antialiased"><main class="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900"><section class="relative min-h-screen flex items-center justify-center overflow-hidden"><div class="absolute inset-0 opacity-10"><svg class="w-full h-full" viewBox="0 0 1000 1000"><defs><pattern id="mesh" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="2" fill="currentColor" class="text-blue-400"></circle></pattern></defs><rect width="100%" height="100%" fill="url(#mesh)"></rect><line x1="100" y1="100" x2="300" y2="200" stroke="currentColor" stroke-width="1" class="text-blue-400 mesh-connect" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></line><line x1="300" y1="200" x2="500" y2="150" stroke="currentColor" stroke-width="1" class="text-blue-400 mesh-connect" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></line><line x1="500" y1="150" x2="700" y2="300" stroke="currentColor" stroke-width="1" class="text-blue-400 mesh-connect" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></line></svg></div><div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><div class="space-y-8" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="flex items-center justify-center space-x-3" style="opacity:0;transform:scale(0.9) translateZ(0)"><div class="relative"><div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><path d="M5 13a10 10 0 0 1 14 0"></path><path d="M8.5 16.5a5 5 0 0 1 7 0"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><line x1="12" x2="12.01" y1="20" y2="20"></line></svg></div><div class="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div></div><h1 class="text-5xl md:text-7xl font-bold font-mono text-white">krtr<span class="text-blue-400">*</span>mesh</h1></div><p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed" style="opacity:0;transform:translateY(20px) translateZ(0)"><span class="gradient-text font-semibold">Decentralized, encrypted, offline-first messaging</span><br/>for the post-platform era.</p><p class="text-lg text-gray-400 max-w-2xl mx-auto font-mono" style="opacity:0;transform:translateY(20px) translateZ(0)">Built from the blackout. Inspired by the streets. Whispered across devices.</p><div class="flex flex-wrap justify-center gap-8 mt-12" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="flex flex-col items-center space-y-2 group cursor-pointer" style="opacity:0;transform:scale(0.8) translateZ(0)"><div class="p-4 rounded-xl bg-dark-800 border border-dark-700 group-hover:border-blue-500 transition-colors text-green-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg></div><span class="text-sm text-gray-400 group-hover:text-white transition-colors">End-to-End Encrypted</span></div><div class="flex flex-col items-center space-y-2 group cursor-pointer" style="opacity:0;transform:scale(0.8) translateZ(0)"><div class="p-4 rounded-xl bg-dark-800 border border-dark-700 group-hover:border-blue-500 transition-colors text-yellow-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg></div><span class="text-sm text-gray-400 group-hover:text-white transition-colors">Zero-Knowledge Proofs</span></div><div class="flex flex-col items-center space-y-2 group cursor-pointer" style="opacity:0;transform:scale(0.8) translateZ(0)"><div class="p-4 rounded-xl bg-dark-800 border border-dark-700 group-hover:border-blue-500 transition-colors text-blue-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><span class="text-sm text-gray-400 group-hover:text-white transition-colors">Mesh Networking</span></div><div class="flex flex-col items-center space-y-2 group cursor-pointer" style="opacity:0;transform:scale(0.8) translateZ(0)"><div class="p-4 rounded-xl bg-dark-800 border border-dark-700 group-hover:border-blue-500 transition-colors text-purple-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6"><path d="M5 13a10 10 0 0 1 14 0"></path><path d="M8.5 16.5a5 5 0 0 1 7 0"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><line x1="12" x2="12.01" y1="20" y2="20"></line></svg></div><span class="text-sm text-gray-400 group-hover:text-white transition-colors">Offline-First</span></div></div><div class="flex flex-col sm:flex-row gap-4 justify-center mt-12" style="opacity:0;transform:translateY(20px) translateZ(0)"><button class="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg">Download App</button><button class="px-8 py-4 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300">Learn More</button></div><div class="absolute bottom-8 left-1/2 transform -translate-x-1/2" style="opacity:0"><div class="w-6 h-10 border-2 border-gray-600 rounded-full flex justify-center"><div class="w-1 h-3 bg-blue-400 rounded-full mt-2"></div></div></div></div></div></section><section class="py-20 bg-dark-900 relative overflow-hidden"><div class="absolute inset-0 opacity-5"><div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div></div><div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(20px) translateZ(0)"><h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Built for <span class="gradient-text">Privacy</span> &amp; <span class="gradient-text">Performance</span></h2><p class="text-xl text-gray-400 max-w-3xl mx-auto">KRTR Mesh combines cutting-edge cryptography with mesh networking to deliver secure, private, and resilient communication that works anywhere.</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105"><div class="w-16 h-16 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 p-4 mb-6 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><rect x="16" y="16" width="6" height="6" rx="1"></rect><rect x="2" y="16" width="6" height="6" rx="1"></rect><rect x="9" y="2" width="6" height="6" rx="1"></rect><path d="M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3"></path><path d="M12 12V8"></path></svg></div><h3 class="text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors">Mesh Networking</h3><p class="text-gray-400 mb-6 leading-relaxed">Multi-hop routing with TTL-based forwarding. Connect up to 20 peers simultaneously with automatic discovery via Bluetooth LE.</p><ul class="space-y-2"><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Automatic peer discovery</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Multi-hop routing (max 7 hops)</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Connection management</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Loop prevention</li></ul></div></div><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105"><div class="w-16 h-16 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 p-4 mb-6 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg></div><h3 class="text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors">End-to-End Encryption</h3><p class="text-gray-400 mb-6 leading-relaxed">Military-grade encryption with X25519 key exchange, AES-256-GCM, and Ed25519 signatures for message authenticity.</p><ul class="space-y-2"><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>X25519 key exchange</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>AES-256-GCM encryption</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Ed25519 signatures</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Forward secrecy</li></ul></div></div><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105"><div class="w-16 h-16 rounded-xl bg-gradient-to-r from-yellow-500 to-orange-500 p-4 mb-6 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg></div><h3 class="text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors">Zero-Knowledge Proofs</h3><p class="text-gray-400 mb-6 leading-relaxed">Anonymous authentication and private reputation using Noir ZK circuits. Prove membership without revealing identity.</p><ul class="space-y-2"><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Anonymous authentication</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Private reputation</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Selective disclosure</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Group authorization</li></ul></div></div><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105"><div class="w-16 h-16 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 p-4 mb-6 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg></div><h3 class="text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors">Store-and-Forward</h3><p class="text-gray-400 mb-6 leading-relaxed">Offline message delivery with intelligent routing. Messages reach their destination even when peers are temporarily offline.</p><ul class="space-y-2"><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Offline message delivery</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Message persistence</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Intelligent routing</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Delivery confirmation</li></ul></div></div><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105"><div class="w-16 h-16 rounded-xl bg-gradient-to-r from-indigo-500 to-purple-500 p-4 mb-6 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path><circle cx="12" cy="12" r="3"></circle></svg></div><h3 class="text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors">Privacy Features</h3><p class="text-gray-400 mb-6 leading-relaxed">Cover traffic generation and timing randomization prevent traffic analysis. Emergency wipe capability for security.</p><ul class="space-y-2"><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Cover traffic generation</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Timing randomization</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Ephemeral identities</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Emergency wipe</li></ul></div></div><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105"><div class="w-16 h-16 rounded-xl bg-gradient-to-r from-red-500 to-pink-500 p-4 mb-6 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><rect width="16" height="10" x="2" y="7" rx="2" ry="2"></rect><line x1="22" x2="22" y1="11" y2="13"></line></svg></div><h3 class="text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors">Battery Optimization</h3><p class="text-gray-400 mb-6 leading-relaxed">4-tier power management with adaptive connection limits. LZ4 compression saves 30-70% bandwidth.</p><ul class="space-y-2"><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>4-tier power management</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Adaptive connections</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>LZ4 compression</li><li class="flex items-center text-sm text-gray-500"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></div>Binary protocol</li></ul></div></div></div><div class="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="text-center"><div class="text-3xl md:text-4xl font-bold text-white mb-2">7<span class="text-blue-400"></span></div><div class="text-gray-400 text-sm">Max Hops</div></div><div class="text-center"><div class="text-3xl md:text-4xl font-bold text-white mb-2">20<span class="text-blue-400"></span></div><div class="text-gray-400 text-sm">Max Connections</div></div><div class="text-center"><div class="text-3xl md:text-4xl font-bold text-white mb-2">70<span class="text-blue-400">%</span></div><div class="text-gray-400 text-sm">Bandwidth Savings</div></div><div class="text-center"><div class="text-3xl md:text-4xl font-bold text-white mb-2">256<span class="text-blue-400">-bit</span></div><div class="text-gray-400 text-sm">AES Encryption</div></div></div></div></section><section class="py-20 bg-gradient-to-br from-dark-800 to-dark-900 relative overflow-hidden"><div class="absolute inset-0 opacity-10"><div class="absolute top-20 left-20 w-32 h-32 bg-blue-500 rounded-full blur-3xl"></div><div class="absolute bottom-20 right-20 w-32 h-32 bg-purple-500 rounded-full blur-3xl"></div></div><div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(20px) translateZ(0)"><h2 class="text-4xl md:text-5xl font-bold text-white mb-6"><span class="gradient-text">Technical</span> Architecture</h2><p class="text-xl text-gray-400 max-w-3xl mx-auto">Built on proven cryptographic primitives and modern networking protocols, KRTR Mesh delivers enterprise-grade security with consumer-friendly usability.</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20"><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300" style="opacity:0;transform:translateX(-20px) translateZ(0)"><div class="flex items-center mb-6"><div class="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 p-3 mr-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><path d="m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z"></path><path d="m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65"></path><path d="m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65"></path></svg></div><h3 class="text-2xl font-bold text-white">Networking</h3></div><div class="space-y-4"><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Bluetooth LE Mesh</div><div class="text-gray-400 text-sm">Low-energy mesh networking</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Multi-hop Routing</div><div class="text-gray-400 text-sm">TTL-based message forwarding</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Store-and-Forward</div><div class="text-gray-400 text-sm">Offline message delivery</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Connection Management</div><div class="text-gray-400 text-sm">Adaptive peer limits</div></div></div></div></div><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300" style="opacity:0;transform:translateX(20px) translateZ(0)"><div class="flex items-center mb-6"><div class="w-12 h-12 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 p-3 mr-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg></div><h3 class="text-2xl font-bold text-white">Cryptography</h3></div><div class="space-y-4"><div class="flex justify-between items-start"><div><div class="text-white font-semibold">X25519 ECDH</div><div class="text-gray-400 text-sm">Key exchange with forward secrecy</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">AES-256-GCM</div><div class="text-gray-400 text-sm">Authenticated encryption</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Ed25519</div><div class="text-gray-400 text-sm">Digital signatures</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Argon2id</div><div class="text-gray-400 text-sm">Password-based key derivation</div></div></div></div></div><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300" style="opacity:0;transform:translateX(-20px) translateZ(0)"><div class="flex items-center mb-6"><div class="w-12 h-12 rounded-xl bg-gradient-to-r from-yellow-500 to-orange-500 p-3 mr-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg></div><h3 class="text-2xl font-bold text-white">Zero-Knowledge</h3></div><div class="space-y-4"><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Noir Circuits</div><div class="text-gray-400 text-sm">ZK proof generation</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Anonymous Auth</div><div class="text-gray-400 text-sm">Identity-preserving verification</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Private Reputation</div><div class="text-gray-400 text-sm">Trust without exposure</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Selective Disclosure</div><div class="text-gray-400 text-sm">Prove without revealing</div></div></div></div></div><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300" style="opacity:0;transform:translateX(20px) translateZ(0)"><div class="flex items-center mb-6"><div class="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 p-3 mr-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><rect x="4" y="4" width="16" height="16" rx="2"></rect><rect x="9" y="9" width="6" height="6"></rect><path d="M15 2v2"></path><path d="M15 20v2"></path><path d="M2 15h2"></path><path d="M2 9h2"></path><path d="M20 15h2"></path><path d="M20 9h2"></path><path d="M9 2v2"></path><path d="M9 20v2"></path></svg></div><h3 class="text-2xl font-bold text-white">Performance</h3></div><div class="space-y-4"><div class="flex justify-between items-start"><div><div class="text-white font-semibold">LZ4 Compression</div><div class="text-gray-400 text-sm">30-70% bandwidth savings</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Binary Protocol</div><div class="text-gray-400 text-sm">Efficient message encoding</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Battery Optimization</div><div class="text-gray-400 text-sm">4-tier power management</div></div></div><div class="flex justify-between items-start"><div><div class="text-white font-semibold">Message Fragmentation</div><div class="text-gray-400 text-sm">Large payload handling</div></div></div></div></div></div><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8" style="opacity:0;transform:translateY(20px) translateZ(0)"><h3 class="text-2xl font-bold text-white mb-8 text-center">System Architecture</h3><div class="space-y-4"><div class="flex items-center p-4 bg-dark-700 rounded-xl hover:bg-dark-600 transition-colors" style="opacity:0;transform:translateX(-20px) translateZ(0)"><div class="w-4 h-4 bg-blue-500 rounded-full mr-4"></div><div class="flex-1"><div class="text-white font-semibold">Application Layer</div><div class="text-gray-400 text-sm">SwiftUI / React Native UI</div></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-gray-500"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><div class="flex items-center p-4 bg-dark-700 rounded-xl hover:bg-dark-600 transition-colors" style="opacity:0;transform:translateX(-20px) translateZ(0)"><div class="w-4 h-4 bg-green-500 rounded-full mr-4"></div><div class="flex-1"><div class="text-white font-semibold">Service Layer</div><div class="text-gray-400 text-sm">Mesh, Crypto, ZK Services</div></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-gray-500"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><div class="flex items-center p-4 bg-dark-700 rounded-xl hover:bg-dark-600 transition-colors" style="opacity:0;transform:translateX(-20px) translateZ(0)"><div class="w-4 h-4 bg-yellow-500 rounded-full mr-4"></div><div class="flex-1"><div class="text-white font-semibold">Protocol Layer</div><div class="text-gray-400 text-sm">KRTR Binary Protocol</div></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-gray-500"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><div class="flex items-center p-4 bg-dark-700 rounded-xl hover:bg-dark-600 transition-colors" style="opacity:0;transform:translateX(-20px) translateZ(0)"><div class="w-4 h-4 bg-purple-500 rounded-full mr-4"></div><div class="flex-1"><div class="text-white font-semibold">Transport Layer</div><div class="text-gray-400 text-sm">Bluetooth LE Abstraction</div></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-gray-500"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><div class="flex items-center p-4 bg-dark-700 rounded-xl hover:bg-dark-600 transition-colors" style="opacity:0;transform:translateX(-20px) translateZ(0)"><div class="w-4 h-4 bg-red-500 rounded-full mr-4"></div><div class="flex-1"><div class="text-white font-semibold">Hardware Layer</div><div class="text-gray-400 text-sm">iOS / Android / macOS</div></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-gray-500"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div></div></div><div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="text-center"><div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg></div><h4 class="text-xl font-bold text-white mb-2">Forward Secrecy</h4><p class="text-gray-400 text-sm leading-relaxed">New keys generated each session ensure past communications remain secure even if current keys are compromised.</p></div><div class="text-center"><div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg></div><h4 class="text-xl font-bold text-white mb-2">Zero Trust</h4><p class="text-gray-400 text-sm leading-relaxed">No central servers or authorities. Every message is cryptographically verified and authenticated.</p></div><div class="text-center"><div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg></div><h4 class="text-xl font-bold text-white mb-2">Privacy by Design</h4><p class="text-gray-400 text-sm leading-relaxed">Cover traffic, timing randomization, and ephemeral identities prevent traffic analysis.</p></div></div></div></section><section class="py-20 bg-dark-900 relative overflow-hidden"><div class="absolute inset-0 bg-gradient-to-r from-blue-900/10 to-purple-900/10"></div><div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(20px) translateZ(0)"><h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Experience <span class="gradient-text">KRTR Mesh</span></h2><p class="text-xl text-gray-400 max-w-3xl mx-auto">Simple, intuitive interfaces that put powerful mesh networking and cryptographic privacy at your fingertips.</p></div><div class="grid grid-cols-1 lg:grid-cols-2 gap-12"><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="flex flex-col lg:flex-row items-center gap-8"><div class="flex-1 space-y-6"><div class="flex items-center space-x-4"><div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><path d="M3 3v18h18"></path><path d="M18 17V9"></path><path d="M13 17V5"></path><path d="M8 17v-3"></path></svg></div><h3 class="text-2xl font-bold text-white">Dashboard</h3></div><p class="text-gray-400 leading-relaxed">Real-time network status, connection metrics, and quick actions all in one place.</p><div class="grid grid-cols-2 gap-3"><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Network Status</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Connected Peers</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Battery Level</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Power Mode</span></div></div></div><div class="flex-shrink-0"><div class="w-64 h-96 bg-dark-800 rounded-3xl border-4 border-dark-700 p-4 group-hover:border-blue-500/50 transition-all duration-300"><div class="bg-dark-700 rounded-2xl h-full p-4 space-y-4"><div class="flex justify-between items-center text-xs text-gray-400"><span>9:41</span><div class="flex space-x-1"><div class="w-4 h-2 bg-green-400 rounded-sm"></div><div class="w-4 h-2 bg-blue-400 rounded-sm"></div><div class="w-4 h-2 bg-gray-600 rounded-sm"></div></div></div><div class="text-center"><h4 class="text-white font-semibold">Dashboard</h4></div><div class="space-y-3 flex-1"><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">Network Status</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Active</span><div class="w-2 h-2 rounded-full bg-green-400"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">Connected Peers</span><div class="flex items-center space-x-2"><span class="text-white text-sm">7</span><div class="w-2 h-2 rounded-full bg-gray-500"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">Battery Level</span><div class="flex items-center space-x-2"><span class="text-white text-sm">85%</span><div class="w-2 h-2 rounded-full bg-gray-500"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">Power Mode</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Balanced</span><div class="w-2 h-2 rounded-full bg-gray-500"></div></div></div></div><div class="flex justify-center"><div class="w-8 h-1 bg-gray-600 rounded-full"></div></div></div></div></div></div></div><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="flex flex-col lg:flex-row items-center gap-8"><div class="flex-1 space-y-6"><div class="flex items-center space-x-4"><div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><rect x="16" y="16" width="6" height="6" rx="1"></rect><rect x="2" y="16" width="6" height="6" rx="1"></rect><rect x="9" y="2" width="6" height="6" rx="1"></rect><path d="M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3"></path><path d="M12 12V8"></path></svg></div><h3 class="text-2xl font-bold text-white">Mesh Network</h3></div><p class="text-gray-400 leading-relaxed">Visualize your mesh connections, peer discovery, and network topology in real-time.</p><div class="grid grid-cols-2 gap-3"><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Peer Discovery</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Connection Map</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Signal Strength</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Routing Info</span></div></div></div><div class="flex-shrink-0"><div class="w-64 h-96 bg-dark-800 rounded-3xl border-4 border-dark-700 p-4 group-hover:border-blue-500/50 transition-all duration-300"><div class="bg-dark-700 rounded-2xl h-full p-4 space-y-4"><div class="flex justify-between items-center text-xs text-gray-400"><span>9:41</span><div class="flex space-x-1"><div class="w-4 h-2 bg-green-400 rounded-sm"></div><div class="w-4 h-2 bg-blue-400 rounded-sm"></div><div class="w-4 h-2 bg-gray-600 rounded-sm"></div></div></div><div class="text-center"><h4 class="text-white font-semibold">Mesh Network</h4></div><div class="space-y-3 flex-1"><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">alice@mesh</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Connected</span><div class="w-2 h-2 rounded-full bg-green-400"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">bob@mesh</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Discovering</span><div class="w-2 h-2 rounded-full bg-yellow-400"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">charlie@mesh</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Connected</span><div class="w-2 h-2 rounded-full bg-green-400"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">diana@mesh</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Offline</span><div class="w-2 h-2 rounded-full bg-red-400"></div></div></div></div><div class="flex justify-center"><div class="w-8 h-1 bg-gray-600 rounded-full"></div></div></div></div></div></div></div><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="flex flex-col lg:flex-row items-center gap-8"><div class="flex-1 space-y-6"><div class="flex items-center space-x-4"><div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg></div><h3 class="text-2xl font-bold text-white">Secure Chat</h3></div><p class="text-gray-400 leading-relaxed">End-to-end encrypted messaging with forward secrecy and anonymous authentication.</p><div class="grid grid-cols-2 gap-3"><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">E2E Encryption</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Group Chats</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">File Sharing</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Message Status</span></div></div></div><div class="flex-shrink-0"><div class="w-64 h-96 bg-dark-800 rounded-3xl border-4 border-dark-700 p-4 group-hover:border-blue-500/50 transition-all duration-300"><div class="bg-dark-700 rounded-2xl h-full p-4 space-y-4"><div class="flex justify-between items-center text-xs text-gray-400"><span>9:41</span><div class="flex space-x-1"><div class="w-4 h-2 bg-green-400 rounded-sm"></div><div class="w-4 h-2 bg-blue-400 rounded-sm"></div><div class="w-4 h-2 bg-gray-600 rounded-sm"></div></div></div><div class="text-center"><h4 class="text-white font-semibold">General Chat</h4></div><div class="space-y-3 flex-1"><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">alice</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Hey everyone! 👋</span><div class="w-2 h-2 rounded-full bg-gray-400"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">bob</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Network is looking good today</span><div class="w-2 h-2 rounded-full bg-gray-400"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">You</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Agreed! Zero issues so far</span><div class="w-2 h-2 rounded-full bg-blue-400"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">charlie</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Love the new ZK features</span><div class="w-2 h-2 rounded-full bg-gray-400"></div></div></div></div><div class="flex justify-center"><div class="w-8 h-1 bg-gray-600 rounded-full"></div></div></div></div></div></div></div><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="flex flex-col lg:flex-row items-center gap-8"><div class="flex-1 space-y-6"><div class="flex items-center space-x-4"><div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg></div><h3 class="text-2xl font-bold text-white">ZK Features</h3></div><p class="text-gray-400 leading-relaxed">Zero-knowledge proofs for anonymous authentication and private reputation systems.</p><div class="grid grid-cols-2 gap-3"><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Anonymous Auth</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Private Reputation</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Proof Generation</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Verification</span></div></div></div><div class="flex-shrink-0"><div class="w-64 h-96 bg-dark-800 rounded-3xl border-4 border-dark-700 p-4 group-hover:border-blue-500/50 transition-all duration-300"><div class="bg-dark-700 rounded-2xl h-full p-4 space-y-4"><div class="flex justify-between items-center text-xs text-gray-400"><span>9:41</span><div class="flex space-x-1"><div class="w-4 h-2 bg-green-400 rounded-sm"></div><div class="w-4 h-2 bg-blue-400 rounded-sm"></div><div class="w-4 h-2 bg-gray-600 rounded-sm"></div></div></div><div class="text-center"><h4 class="text-white font-semibold">ZK Dashboard</h4></div><div class="space-y-3 flex-1"><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">Identity Proof</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Generated</span><div class="w-2 h-2 rounded-full bg-green-400"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">Reputation Score</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Hidden</span><div class="w-2 h-2 rounded-full bg-purple-400"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">Group Membership</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Verified</span><div class="w-2 h-2 rounded-full bg-green-400"></div></div></div><div class="flex justify-between items-center p-2 bg-dark-600 rounded-lg"><span class="text-gray-300 text-sm">Anonymous Mode</span><div class="flex items-center space-x-2"><span class="text-white text-sm">Active</span><div class="w-2 h-2 rounded-full bg-purple-400"></div></div></div></div><div class="flex justify-center"><div class="w-8 h-1 bg-gray-600 rounded-full"></div></div></div></div></div></div></div></div><div class="mt-20 text-center" style="opacity:0;transform:translateY(20px) translateZ(0)"><h3 class="text-2xl font-bold text-white mb-8">Available on All Platforms</h3><div class="flex justify-center space-x-8"><div class="flex flex-col items-center space-y-2"><div class="text-4xl">📱</div><span class="text-gray-400">iOS</span></div><div class="flex flex-col items-center space-y-2"><div class="text-4xl">🤖</div><span class="text-gray-400">Android</span></div><div class="flex flex-col items-center space-y-2"><div class="text-4xl">💻</div><span class="text-gray-400">macOS</span></div></div></div></div></section><section class="py-20 bg-gradient-to-br from-dark-800 to-dark-900 relative overflow-hidden"><div class="absolute inset-0 opacity-10"><div class="absolute top-10 left-10 w-40 h-40 bg-blue-500 rounded-full blur-3xl"></div><div class="absolute bottom-10 right-10 w-40 h-40 bg-purple-500 rounded-full blur-3xl"></div></div><div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(20px) translateZ(0)"><h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Get <span class="gradient-text">KRTR Mesh</span></h2><p class="text-xl text-gray-400 max-w-3xl mx-auto">Join the decentralized communication revolution. Download KRTR Mesh and start building resilient networks today.</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105"><div class="flex items-center justify-between mb-6"><div class="flex items-center space-x-3"><div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><rect width="14" height="20" x="5" y="2" rx="2" ry="2"></rect><path d="M12 18h.01"></path></svg></div><h3 class="text-2xl font-bold text-white">iOS</h3></div><span class="px-3 py-1 bg-yellow-500 text-white text-xs font-semibold rounded-full">Coming Soon</span></div><p class="text-gray-400 mb-6 leading-relaxed">Native iOS app with full mesh networking capabilities</p><div class="space-y-2 mb-8"><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Native Swift UI</span></div><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Background mesh</span></div><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Biometric unlock</span></div><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">AirDrop integration</span></div></div><button class="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 group-hover:scale-105" disabled=""><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" x2="12" y1="15" y2="3"></line></svg><span>Notify Me</span></button></div></div><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105"><div class="flex items-center justify-between mb-6"><div class="flex items-center space-x-3"><div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><line x1="12" x2="12.01" y1="18" y2="18"></line></svg></div><h3 class="text-2xl font-bold text-white">Android</h3></div><span class="px-3 py-1 bg-blue-500 text-white text-xs font-semibold rounded-full">In Development</span></div><p class="text-gray-400 mb-6 leading-relaxed">Android app with Nearby Connections API integration</p><div class="space-y-2 mb-8"><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Material Design</span></div><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Background sync</span></div><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Fingerprint auth</span></div><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Quick share</span></div></div><button class="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 group-hover:scale-105"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" x2="12" y1="15" y2="3"></line></svg><span>Join Beta</span></button></div></div><div class="group" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 h-full hover:border-blue-500/50 transition-all duration-300 hover:transform hover:scale-105"><div class="flex items-center justify-between mb-6"><div class="flex items-center space-x-3"><div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-white"><rect width="20" height="14" x="2" y="3" rx="2"></rect><line x1="8" x2="16" y1="21" y2="21"></line><line x1="12" x2="12" y1="17" y2="21"></line></svg></div><h3 class="text-2xl font-bold text-white">macOS</h3></div><span class="px-3 py-1 bg-green-500 text-white text-xs font-semibold rounded-full">Beta Available</span></div><p class="text-gray-400 mb-6 leading-relaxed">Desktop app for mesh coordination and development</p><div class="space-y-2 mb-8"><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Native macOS</span></div><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Developer tools</span></div><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Network analysis</span></div><div class="flex items-center space-x-2"><div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div><span class="text-sm text-gray-300">Bridge mode</span></div></div><button class="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 group-hover:scale-105"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" x2="12" y1="15" y2="3"></line></svg><span>Download</span></button></div></div></div><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 text-center" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="flex justify-center mb-6"><div class="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8 text-white"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></div></div><h3 class="text-2xl font-bold text-white mb-4">Open Source &amp; Transparent</h3><p class="text-gray-400 mb-8 max-w-2xl mx-auto">KRTR Mesh is built in the open. Audit our code, contribute features, or build your own mesh applications using our protocols.</p><div class="flex flex-col sm:flex-row gap-4 justify-center"><button class="px-6 py-3 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300 flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg><span>View Source</span></button><button class="px-6 py-3 border border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-blue-500 hover:text-white transition-all duration-300 flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg><span>Documentation</span></button></div></div><div class="mt-16 text-center" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border border-blue-500/30 rounded-2xl p-8"><h3 class="text-2xl font-bold text-white mb-4">Join the Beta Program</h3><p class="text-gray-300 mb-6 max-w-2xl mx-auto">Get early access to KRTR Mesh, help shape the future of decentralized communication, and be part of the privacy revolution.</p><div class="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto"><input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"/><button class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300">Join Beta</button></div></div></div></div></section><footer class="bg-dark-900 border-t border-dark-700"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-12"><div class="lg:col-span-2"><div class="space-y-4" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="flex items-center space-x-3"><div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-white"><path d="M5 13a10 10 0 0 1 14 0"></path><path d="M8.5 16.5a5 5 0 0 1 7 0"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><line x1="12" x2="12.01" y1="20" y2="20"></line></svg></div><h3 class="text-2xl font-bold font-mono text-white">krtr<span class="text-blue-400">*</span>mesh</h3></div><p class="text-gray-400 leading-relaxed max-w-sm">Decentralized, encrypted, offline-first messaging for the post-platform era. Built from the blackout. Inspired by the streets.</p><div class="flex space-x-4"><div class="flex items-center space-x-2 text-green-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg><span class="text-xs">Encrypted</span></div><div class="flex items-center space-x-2 text-yellow-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg><span class="text-xs">Zero-Knowledge</span></div><div class="flex items-center space-x-2 text-blue-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M5 13a10 10 0 0 1 14 0"></path><path d="M8.5 16.5a5 5 0 0 1 7 0"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><line x1="12" x2="12.01" y1="20" y2="20"></line></svg><span class="text-xs">Mesh Network</span></div></div></div></div><div class="space-y-4" style="opacity:0;transform:translateY(20px) translateZ(0)"><h4 class="text-white font-semibold">Product</h4><ul class="space-y-2"><li><a href="#features" class="text-gray-400 hover:text-white transition-colors text-sm">Features</a></li><li><a href="#tech" class="text-gray-400 hover:text-white transition-colors text-sm">Technology</a></li><li><a href="#download" class="text-gray-400 hover:text-white transition-colors text-sm">Download</a></li><li><a href="#roadmap" class="text-gray-400 hover:text-white transition-colors text-sm">Roadmap</a></li></ul></div><div class="space-y-4" style="opacity:0;transform:translateY(20px) translateZ(0)"><h4 class="text-white font-semibold">Developers</h4><ul class="space-y-2"><li><a href="#docs" class="text-gray-400 hover:text-white transition-colors text-sm">Documentation</a></li><li><a href="#api" class="text-gray-400 hover:text-white transition-colors text-sm">API Reference</a></li><li><a href="https://github.com/Z0rlord/krtr-mesh" class="text-gray-400 hover:text-white transition-colors text-sm">GitHub</a></li><li><a href="#contributing" class="text-gray-400 hover:text-white transition-colors text-sm">Contributing</a></li></ul></div><div class="space-y-4" style="opacity:0;transform:translateY(20px) translateZ(0)"><h4 class="text-white font-semibold">Community</h4><ul class="space-y-2"><li><a href="#discord" class="text-gray-400 hover:text-white transition-colors text-sm">Discord</a></li><li><a href="#twitter" class="text-gray-400 hover:text-white transition-colors text-sm">Twitter</a></li><li><a href="#blog" class="text-gray-400 hover:text-white transition-colors text-sm">Blog</a></li><li><a href="#support" class="text-gray-400 hover:text-white transition-colors text-sm">Support</a></li></ul></div><div class="space-y-4" style="opacity:0;transform:translateY(20px) translateZ(0)"><h4 class="text-white font-semibold">Legal</h4><ul class="space-y-2"><li><a href="#privacy" class="text-gray-400 hover:text-white transition-colors text-sm">Privacy Policy</a></li><li><a href="#terms" class="text-gray-400 hover:text-white transition-colors text-sm">Terms of Service</a></li><li><a href="#security" class="text-gray-400 hover:text-white transition-colors text-sm">Security</a></li><li><a href="#license" class="text-gray-400 hover:text-white transition-colors text-sm">License</a></li></ul></div></div><div class="bg-dark-800 border border-dark-700 rounded-2xl p-8 mb-12" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="text-center max-w-2xl mx-auto"><h3 class="text-2xl font-bold text-white mb-4">Stay Updated</h3><p class="text-gray-400 mb-6">Get the latest updates on KRTR Mesh development, security advisories, and community news delivered to your inbox.</p><div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto"><input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 bg-dark-700 border border-dark-600 rounded-xl text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"/><button class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300">Subscribe</button></div></div></div><div class="flex flex-col md:flex-row justify-between items-center pt-8 border-t border-dark-700" style="opacity:0;transform:translateY(20px) translateZ(0)"><div class="text-gray-400 text-sm mb-4 md:mb-0">© 2024 KRTR Team. Built for the people, by the people.</div><div class="flex space-x-6"><a href="https://github.com/Z0rlord/krtr-mesh" class="text-gray-400 hover:text-white transition-colors" aria-label="GitHub"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></a><a href="#twitter" class="text-gray-400 hover:text-white transition-colors" aria-label="Twitter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg></a><a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white transition-colors" aria-label="Email"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg></a></div></div><div class="text-center mt-8" style="opacity:0"><p class="text-gray-500 text-sm font-mono">🕳️ We build from the edge.</p><p class="text-gray-600 text-xs mt-2 italic">&quot;Decentralized by design. Privacy by default. Built for the people.&quot;</p></div></div></footer></main><script src="/_next/static/chunks/webpack-0f866bb3c58b1f53.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/bb3ef058b751a6ad-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n3:HL[\"/_next/static/css/ee2406ec3f3397fb.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"4:I[2846,[],\"\"]\n6:I[9107,[],\"ClientPageRoot\"]\n7:I[6,[\"755\",\"static/chunks/755-1b84a57102143abe.js\",\"931\",\"static/chunks/app/page-10fdf8e5ef664973.js\"],\"default\",1]\n8:I[4707,[],\"\"]\n9:I[6423,[],\"\"]\nb:I[1060,[],\"\"]\nc:[]\n"])</script><script>self.__next_f.push([1,"0:[\"$\",\"$L4\",null,{\"buildId\":\"DrxacXKQcZU5y4b-qQisL\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"\"],\"initialTree\":[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"__PAGE__\",{},[[\"$L5\",[\"$\",\"$L6\",null,{\"props\":{\"params\":{},\"searchParams\":{}},\"Component\":\"$7\"}],null],null],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ee2406ec3f3397fb.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"__variable_e8ce0c __variable_3c557b\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c antialiased\",\"children\":[\"$\",\"$L8\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[]}]}]}]],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$La\"],\"globalErrorComponent\":\"$b\",\"missingSlots\":\"$Wc\"}]\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"KRTR Mesh - Decentralized Encrypted Messaging\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Decentralized, encrypted, offline-first messaging for the post-platform era. Built from the blackout. Inspired by the streets.\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"KRTR Team\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"mesh networking,decentralized messaging,privacy,encryption,zero-knowledge,offline-first,bluetooth,peer-to-peer\"}],[\"$\",\"meta\",\"6\",{\"name\":\"creator\",\"content\":\"KRTR Team\"}],[\"$\",\"meta\",\"7\",{\"name\":\"publisher\",\"content\":\"KRTR Team\"}],[\"$\",\"meta\",\"8\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"9\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:title\",\"content\":\"KRTR Mesh - Decentralized Encrypted Messaging\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:description\",\"content\":\"Decentralized, encrypted, offline-first messaging for the post-platform era.\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:url\",\"content\":\"https://krtr-mesh.vercel.app/\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:site_name\",\"content\":\"KRTR Mesh\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image\",\"content\":\"http://localhost:3000/og-image.png\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:image:alt\",\"content\":\"KRTR Mesh - Decentralized Messaging\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:title\",\"content\":\"KRTR Mesh - Decentralized Encrypted Messaging\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:description\",\"content\":\"Decentralized, encrypted, offline-first messaging for the post-platform era.\"}],[\"$\",\"meta\",\"23\",{\"name\":\"twitter:image\",\"content\":\"http://localhost:3000/og-image.png\"}],[\"$\",\"meta\",\"24\",{\"name\":\"next-size-adjust\"}]]\n"])</script><script>self.__next_f.push([1,"5:null\n"])</script></body></html>