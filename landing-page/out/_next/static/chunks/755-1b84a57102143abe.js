"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[755],{9763:function(t,e,i){i.d(e,{Z:function(){return o}});var n=i(2265),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(t,e)=>{let i=(0,n.forwardRef)((i,o)=>{let{color:a="currentColor",size:l=24,strokeWidth:u=2,absoluteStrokeWidth:h,children:c,...d}=i;return(0,n.createElement)("svg",{ref:o,...r,width:l,height:l,stroke:a,strokeWidth:h?24*Number(u)/Number(l):u,className:"lucide lucide-".concat(s(t)),...d},[...e.map(t=>{let[e,i]=t;return(0,n.createElement)(e,i)}),...(Array.isArray(c)?c:[c])||[]])});return i.displayName="".concat(t),i}},6221:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},5673:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Battery",[["rect",{width:"16",height:"10",x:"2",y:"7",rx:"2",ry:"2",key:"1w10f2"}],["line",{x1:"22",x2:"22",y1:"11",y2:"13",key:"4dh1rd"}]])},4935:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},4822:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},91:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},2735:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},6362:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},2208:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5135:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},8124:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]])},9345:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},2718:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},9897:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},3420:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]])},8906:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},3388:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},8553:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Tablet",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["line",{x1:"12",x2:"12.01",y1:"18",y2:"18",key:"1dp563"}]])},2351:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},5805:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},2926:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]])},1239:function(t,e,i){i.d(e,{Z:function(){return n}});let n=(0,i(9763).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},3287:function(t,e,i){let n;i.d(e,{E:function(){return rX}});var r,s,o=i(2265);let a=(0,o.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),l=(0,o.createContext)({}),u=(0,o.createContext)(null),h="undefined"!=typeof document,c=h?o.useLayoutEffect:o.useEffect,d=(0,o.createContext)({strict:!1}),p=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),m="data-"+p("framerAppearId");function f(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function y(t){return"string"==typeof t||Array.isArray(t)}function g(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}let v=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],x=["initial",...v];function P(t){return g(t.animate)||x.some(e=>y(t[e]))}function b(t){return!!(P(t)||t.variants)}function w(t){return Array.isArray(t)?t.join(" "):t}let T={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},A={};for(let t in T)A[t]={isEnabled:e=>T[t].some(t=>!!e[t])};let k=(0,o.createContext)({}),V=(0,o.createContext)({}),S=Symbol.for("motionComponentSymbol"),M=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function E(t){if("string"!=typeof t||t.includes("-"));else if(M.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}let C={},D=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],L=new Set(D);function R(t,{layout:e,layoutId:i}){return L.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!C[t]||"opacity"===t)}let j=t=>!!(t&&t.getVelocity),F={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},B=D.length,O=t=>e=>"string"==typeof e&&e.startsWith(t),U=O("--"),I=O("var(--"),N=(t,e)=>e&&"number"==typeof t?e.transform(t):t,Z=(t,e,i)=>Math.min(Math.max(i,t),e),$={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},W={...$,transform:t=>Z(0,1,t)},z={...$,default:1},H=t=>Math.round(1e5*t)/1e5,q=/(-)?([\d]*\.?[\d])+/g,Y=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,X=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function G(t){return"string"==typeof t}let K=t=>({test:e=>G(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),_=K("deg"),J=K("%"),Q=K("px"),tt=K("vh"),te=K("vw"),ti={...J,parse:t=>J.parse(t)/100,transform:t=>J.transform(100*t)},tn={...$,transform:Math.round},tr={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,size:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,rotate:_,rotateX:_,rotateY:_,rotateZ:_,scale:z,scaleX:z,scaleY:z,scaleZ:z,skew:_,skewX:_,skewY:_,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:W,originX:ti,originY:ti,originZ:Q,zIndex:tn,fillOpacity:W,strokeOpacity:W,numOctaves:tn};function ts(t,e,i,n){let{style:r,vars:s,transform:o,transformOrigin:a}=t,l=!1,u=!1,h=!0;for(let t in e){let i=e[t];if(U(t)){s[t]=i;continue}let n=tr[t],c=N(i,n);if(L.has(t)){if(l=!0,o[t]=c,!h)continue;i!==(n.default||0)&&(h=!1)}else t.startsWith("origin")?(u=!0,a[t]=c):r[t]=c}if(!e.transform&&(l||n?r.transform=function(t,{enableHardwareAcceleration:e=!0,allowTransformNone:i=!0},n,r){let s="";for(let e=0;e<B;e++){let i=D[e];if(void 0!==t[i]){let e=F[i]||i;s+=`${e}(${t[i]}) `}}return e&&!t.z&&(s+="translateZ(0)"),s=s.trim(),r?s=r(t,n?"":s):i&&n&&(s="none"),s}(t.transform,i,h,n):r.transform&&(r.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;r.transformOrigin=`${t} ${e} ${i}`}}let to=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ta(t,e,i){for(let n in e)j(e[n])||R(n,i)||(t[n]=e[n])}let tl=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tu(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||tl.has(t)}let th=t=>!tu(t);try{(r=require("@emotion/is-prop-valid").default)&&(th=t=>t.startsWith("on")?!tu(t):r(t))}catch(t){}function tc(t,e,i){return"string"==typeof t?t:Q.transform(e+i*t)}let td={offset:"stroke-dashoffset",array:"stroke-dasharray"},tp={offset:"strokeDashoffset",array:"strokeDasharray"};function tm(t,{attrX:e,attrY:i,attrScale:n,originX:r,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,c,d){if(ts(t,u,h,d),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:p,style:m,dimensions:f}=t;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==r||void 0!==s||m.transform)&&(m.transformOrigin=function(t,e,i){let n=tc(e,t.x,t.width),r=tc(i,t.y,t.height);return`${n} ${r}`}(f,void 0!==r?r:.5,void 0!==s?s:.5)),void 0!==e&&(p.x=e),void 0!==i&&(p.y=i),void 0!==n&&(p.scale=n),void 0!==o&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?td:tp;t[s.offset]=Q.transform(-n);let o=Q.transform(e),a=Q.transform(i);t[s.array]=`${o} ${a}`}(p,o,a,l,!1)}let tf=()=>({...to(),attrs:{}}),ty=t=>"string"==typeof t&&"svg"===t.toLowerCase();function tg(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}let tv=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tx(t,e,i,n){for(let i in tg(t,e,void 0,n),e.attrs)t.setAttribute(tv.has(i)?i:p(i),e.attrs[i])}function tP(t,e){let{style:i}=t,n={};for(let r in i)(j(i[r])||e.style&&j(e.style[r])||R(r,t))&&(n[r]=i[r]);return n}function tb(t,e){let i=tP(t,e);for(let n in t)(j(t[n])||j(e[n]))&&(i[-1!==D.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]);return i}function tw(t,e,i,n={},r={}){return"function"==typeof e&&(e=e(void 0!==i?i:t.custom,n,r)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==i?i:t.custom,n,r)),e}let tT=t=>Array.isArray(t),tA=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),tk=t=>tT(t)?t[t.length-1]||0:t;function tV(t){let e=j(t)?t.get():t;return tA(e)?e.toValue():e}let tS=t=>(e,i)=>{let n=(0,o.useContext)(l),r=(0,o.useContext)(u),s=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:i},n,r,s){let o={latestValues:function(t,e,i,n){let r={},s=n(t,{});for(let t in s)r[t]=tV(s[t]);let{initial:o,animate:a}=t,l=P(t),u=b(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!i&&!1===i.initial,c=(h=h||!1===o)?a:o;return c&&"boolean"!=typeof c&&!g(c)&&(Array.isArray(c)?c:[c]).forEach(e=>{let i=tw(t,e);if(!i)return;let{transitionEnd:n,transition:s,...o}=i;for(let t in o){let e=o[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let t in n)r[t]=n[t]}),r}(n,r,s,t),renderState:e()};return i&&(o.mount=t=>i(n,t,o)),o})(t,e,n,r);return i?s():function(t){let e=(0,o.useRef)(null);return null===e.current&&(e.current=t()),e.current}(s)},tM=t=>t;class tE{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){let e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}let tC=["prepare","read","update","preRender","render","postRender"],{schedule:tD,cancel:tL,state:tR,steps:tj}=function(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=tC.reduce((t,e)=>(t[e]=function(t){let e=new tE,i=new tE,n=0,r=!1,s=!1,o=new WeakSet,a={schedule:(t,s=!1,a=!1)=>{let l=a&&r,u=l?e:i;return s&&o.add(t),u.add(t)&&l&&r&&(n=e.order.length),t},cancel:t=>{i.remove(t),o.delete(t)},process:l=>{if(r){s=!0;return}if(r=!0,[e,i]=[i,e],i.clear(),n=e.order.length)for(let i=0;i<n;i++){let n=e.order[i];n(l),o.has(n)&&(a.schedule(n),t())}r=!1,s&&(s=!1,a.process(l))}};return a}(()=>i=!0),t),{}),o=t=>s[t].process(r),a=()=>{let s=performance.now();i=!1,r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1),r.timestamp=s,r.isProcessing=!0,tC.forEach(o),r.isProcessing=!1,i&&e&&(n=!1,t(a))},l=()=>{i=!0,n=!0,r.isProcessing||t(a)};return{schedule:tC.reduce((t,e)=>{let n=s[e];return t[e]=(t,e=!1,r=!1)=>(i||l(),n.schedule(t,e,r)),t},{}),cancel:t=>tC.forEach(e=>s[e].cancel(t)),state:r,steps:s}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:tM,!0),tF={useVisualState:tS({scrapeMotionValuesFromProps:tb,createRenderState:tf,onMount:(t,e,{renderState:i,latestValues:n})=>{tD.read(()=>{try{i.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){i.dimensions={x:0,y:0,width:0,height:0}}}),tD.render(()=>{tm(i,n,{enableHardwareAcceleration:!1},ty(e.tagName),t.transformTemplate),tx(e,i)})}})},tB={useVisualState:tS({scrapeMotionValuesFromProps:tP,createRenderState:to})};function tO(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let tU=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tI(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}let tN=t=>e=>tU(e)&&t(e,tI(e));function tZ(t,e,i,n){return tO(t,e,tN(i),n)}let t$=(t,e)=>i=>e(t(i)),tW=(...t)=>t.reduce(t$);function tz(t){let e=null;return()=>null===e&&(e=t,()=>{e=null})}let tH=tz("dragHorizontal"),tq=tz("dragVertical");function tY(t){let e=!1;if("y"===t)e=tq();else if("x"===t)e=tH();else{let t=tH(),i=tq();t&&i?e=()=>{t(),i()}:(t&&t(),i&&i())}return e}function tX(){let t=tY(!0);return!t||(t(),!1)}class tG{constructor(t){this.isMounted=!1,this.node=t}update(){}}function tK(t,e){let i="onHover"+(e?"Start":"End");return tZ(t.current,"pointer"+(e?"enter":"leave"),(n,r)=>{if("touch"===n.pointerType||tX())return;let s=t.getProps();t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",e),s[i]&&tD.update(()=>s[i](n,r))},{passive:!t.getProps()[i]})}class t_ extends tG{mount(){this.unmount=tW(tK(this.node,!0),tK(this.node,!1))}unmount(){}}class tJ extends tG{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tW(tO(this.node.current,"focus",()=>this.onFocus()),tO(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let tQ=(t,e)=>!!e&&(t===e||tQ(t,e.parentElement));function t0(t,e){if(!e)return;let i=new PointerEvent("pointer"+t);e(i,tI(i))}class t1 extends tG{constructor(){super(...arguments),this.removeStartListeners=tM,this.removeEndListeners=tM,this.removeAccessibleListeners=tM,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();let i=this.node.getProps(),n=tZ(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;let{onTap:i,onTapCancel:n,globalTapTarget:r}=this.node.getProps();tD.update(()=>{r||tQ(this.node.current,t.target)?i&&i(t,e):n&&n(t,e)})},{passive:!(i.onTap||i.onPointerUp)}),r=tZ(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(i.onTapCancel||i.onPointerCancel)});this.removeEndListeners=tW(n,r),this.startPress(t,e)},this.startAccessiblePress=()=>{let t=tO(this.node.current,"keydown",t=>{"Enter"!==t.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=tO(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&t0("up",(t,e)=>{let{onTap:i}=this.node.getProps();i&&tD.update(()=>i(t,e))})}),t0("down",(t,e)=>{this.startPress(t,e)}))}),e=tO(this.node.current,"blur",()=>{this.isPressing&&t0("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=tW(t,e)}}startPress(t,e){this.isPressing=!0;let{onTapStart:i,whileTap:n}=this.node.getProps();n&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),i&&tD.update(()=>i(t,e))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!tX()}cancelPress(t,e){if(!this.checkPressEnd())return;let{onTapCancel:i}=this.node.getProps();i&&tD.update(()=>i(t,e))}mount(){let t=this.node.getProps(),e=tZ(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),i=tO(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=tW(e,i)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let t2=new WeakMap,t5=new WeakMap,t3=t=>{let e=t2.get(t.target);e&&e(t)},t6=t=>{t.forEach(t3)},t9={some:0,all:1};class t4 extends tG{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:t9[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;t5.has(i)||t5.set(i,{});let n=t5.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(t6,{root:t,...e})),n[r]}(e);return t2.set(t,i),n.observe(t),()=>{t2.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}function t7(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function t8(t,e,i){let n=t.getProps();return tw(n,e,void 0!==i?i:n.custom,function(t){let e={};return t.values.forEach((t,i)=>e[i]=t.get()),e}(t),function(t){let e={};return t.values.forEach((t,i)=>e[i]=t.getVelocity()),e}(t))}let et=t=>1e3*t,ee=t=>t/1e3,ei={current:!1},en=t=>Array.isArray(t)&&"number"==typeof t[0],er=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,es={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:er([0,.65,.55,1]),circOut:er([.55,0,1,.45]),backIn:er([.31,.01,.66,-.59]),backOut:er([.33,1.53,.69,.99])},eo=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function ea(t,e,i,n){if(t===e&&i===n)return tM;let r=e=>(function(t,e,i,n,r){let s,o;let a=0;do(s=eo(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:eo(r(t),e,n)}let el=ea(.42,0,1,1),eu=ea(0,0,.58,1),eh=ea(.42,0,.58,1),ec=t=>Array.isArray(t)&&"number"!=typeof t[0],ed=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ep=t=>e=>1-t(1-e),em=t=>1-Math.sin(Math.acos(t)),ef=ep(em),ey=ed(em),eg=ea(.33,1.53,.69,.99),ev=ep(eg),ex=ed(ev),eP={linear:tM,easeIn:el,easeInOut:eh,easeOut:eu,circIn:em,circInOut:ey,circOut:ef,backIn:ev,backInOut:ex,backOut:eg,anticipate:t=>(t*=2)<1?.5*ev(t):.5*(2-Math.pow(2,-10*(t-1)))},eb=t=>{if(Array.isArray(t)){tM(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return ea(e,i,n,r)}return"string"==typeof t?(tM(void 0!==eP[t],`Invalid easing type '${t}'`),eP[t]):t},ew=(t,e)=>i=>!!(G(i)&&X.test(i)&&i.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(i,e)),eT=(t,e,i)=>n=>{if(!G(n))return n;let[r,s,o,a]=n.match(q);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},eA=t=>Z(0,255,t),ek={...$,transform:t=>Math.round(eA(t))},eV={test:ew("rgb","red"),parse:eT("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+ek.transform(t)+", "+ek.transform(e)+", "+ek.transform(i)+", "+H(W.transform(n))+")"},eS={test:ew("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:eV.transform},eM={test:ew("hsl","hue"),parse:eT("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+J.transform(H(e))+", "+J.transform(H(i))+", "+H(W.transform(n))+")"},eE={test:t=>eV.test(t)||eS.test(t)||eM.test(t),parse:t=>eV.test(t)?eV.parse(t):eM.test(t)?eM.parse(t):eS.parse(t),transform:t=>G(t)?t:t.hasOwnProperty("red")?eV.transform(t):eM.transform(t)},eC=(t,e,i)=>-i*t+i*e+t;function eD(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}let eL=(t,e,i)=>{let n=t*t;return Math.sqrt(Math.max(0,i*(e*e-n)+n))},eR=[eS,eV,eM],ej=t=>eR.find(e=>e.test(t));function eF(t){let e=ej(t);tM(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`);let i=e.parse(t);return e===eM&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=eD(a,n,t+1/3),s=eD(a,n,t),o=eD(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let eB=(t,e)=>{let i=eF(t),n=eF(e),r={...i};return t=>(r.red=eL(i.red,n.red,t),r.green=eL(i.green,n.green,t),r.blue=eL(i.blue,n.blue,t),r.alpha=eC(i.alpha,n.alpha,t),eV.transform(r))},eO={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:tM},eU={regex:Y,countKey:"Colors",token:"${c}",parse:eE.parse},eI={regex:q,countKey:"Numbers",token:"${n}",parse:$.parse};function eN(t,{regex:e,countKey:i,token:n,parse:r}){let s=t.tokenised.match(e);s&&(t["num"+i]=s.length,t.tokenised=t.tokenised.replace(e,n),t.values.push(...s.map(r)))}function eZ(t){let e=t.toString(),i={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return i.value.includes("var(--")&&eN(i,eO),eN(i,eU),eN(i,eI),i}function e$(t){return eZ(t).values}function eW(t){let{values:e,numColors:i,numVars:n,tokenised:r}=eZ(t),s=e.length;return t=>{let e=r;for(let r=0;r<s;r++)e=r<n?e.replace(eO.token,t[r]):r<n+i?e.replace(eU.token,eE.transform(t[r])):e.replace(eI.token,H(t[r]));return e}}let ez=t=>"number"==typeof t?0:t,eH={test:function(t){var e,i;return isNaN(t)&&G(t)&&((null===(e=t.match(q))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(Y))||void 0===i?void 0:i.length)||0)>0},parse:e$,createTransformer:eW,getAnimatableNone:function(t){let e=e$(t);return eW(t)(e.map(ez))}},eq=(t,e)=>i=>`${i>0?e:t}`;function eY(t,e){return"number"==typeof t?i=>eC(t,e,i):eE.test(t)?eB(t,e):t.startsWith("var(")?eq(t,e):eK(t,e)}let eX=(t,e)=>{let i=[...t],n=i.length,r=t.map((t,i)=>eY(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}},eG=(t,e)=>{let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=eY(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}},eK=(t,e)=>{let i=eH.createTransformer(e),n=eZ(t),r=eZ(e);return n.numVars===r.numVars&&n.numColors===r.numColors&&n.numNumbers>=r.numNumbers?tW(eX(n.values,r.values),i):(tM(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eq(t,e))},e_=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n},eJ=(t,e)=>i=>eC(t,e,i);function eQ(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(tM(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let n=[],r=i||function(t){if("number"==typeof t);else if("string"==typeof t)return eE.test(t)?eB:eK;else if(Array.isArray(t))return eX;else if("object"==typeof t)return eG;return eJ}(t[0]),s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=tW(Array.isArray(e)?e[i]||tM:e,s)),n.push(s)}return n}(e,n,r),a=o.length,l=e=>{let i=0;if(a>1)for(;i<t.length-2&&!(e<t[i+1]);i++);let n=e_(t[i],t[i+1],e);return o[i](n)};return i?e=>l(Z(t[0],t[s-1],e)):l}function e0({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let r=ec(n)?n.map(eb):eb(n),s={done:!1,value:e[0]},o=eQ((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=e_(0,e,n);t.push(eC(i,1,r))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(r)?r:e.map(()=>r||eh).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}function e1(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}function e2(t,e){return t*Math.sqrt(1-e*e)}let e5=["duration","bounce"],e3=["stiffness","damping","mass"];function e6(t,e){return e.some(e=>void 0!==t[e])}function e9({keyframes:t,restDelta:e,restSpeed:i,...n}){let r;let s=t[0],o=t[t.length-1],a={done:!1,value:s},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!e6(t,e3)&&e6(t,e5)){let i=function({duration:t=800,bounce:e=.25,velocity:i=0,mass:n=1}){let r,s;tM(t<=et(10),"Spring duration must be 10 seconds or less");let o=1-e;o=Z(.05,1,o),t=Z(.01,10,ee(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/e2(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=e2(Math.pow(e,2),o);return(n*i+i-s)*Math.exp(-n)*(-r(e)+.001>0?-1:1)/a}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=et(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:1}).isResolvedFromDuration=!0}return e}({...n,velocity:-ee(n.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*h)),y=o-s,g=ee(Math.sqrt(l/h)),v=5>Math.abs(y);if(i||(i=v?.01:2),e||(e=v?.005:.5),f<1){let t=e2(g,f);r=e=>o-Math.exp(-f*g*e)*((m+f*g*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===f)r=t=>o-Math.exp(-g*t)*(y+(m+g*y)*t);else{let t=g*Math.sqrt(f*f-1);r=e=>{let i=Math.exp(-f*g*e),n=Math.min(t*e,300);return o-i*((m+f*g*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}return{calculatedDuration:p&&c||null,next:t=>{let n=r(t);if(p)a.done=t>=c;else{let s=m;0!==t&&(s=f<1?e1(r,t,n):0);let l=Math.abs(s)<=i,u=Math.abs(o-n)<=e;a.done=l&&u}return a.value=a.done?o:n,a}}}function e4({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,y=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,v=p+g,x=void 0===o?v:o(v);x!==v&&(g=x-p);let P=t=>-g*Math.exp(-t/n),b=t=>x+P(t),w=t=>{let e=P(t),i=b(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},T=t=>{f(m.value)&&(c=t,d=e9({keyframes:[m.value,y(m.value)],velocity:e1(b,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,w(t),T(t)),void 0!==c&&t>c)?d.next(t-c):(e||w(t),m)}}}let e7=t=>{let e=({timestamp:e})=>t(e);return{start:()=>tD.update(e,!0),stop:()=>tL(e),now:()=>tR.isProcessing?tR.timestamp:performance.now()}};function e8(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}let it={decay:e4,inertia:e4,tween:e0,keyframes:e0,spring:e9};function ie({autoplay:t=!0,delay:e=0,driver:i=e7,keyframes:n,type:r="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:h,onUpdate:c,...d}){let p,m,f,y,g,v=1,x=!1,P=()=>{m=new Promise(t=>{p=t})};P();let b=it[r]||e0;b!==e0&&"number"!=typeof n[0]&&(y=eQ([0,100],n,{clamp:!1}),n=[0,100]);let w=b({...d,keyframes:n});"mirror"===a&&(g=b({...d,keyframes:[...n].reverse(),velocity:-(d.velocity||0)}));let T="idle",A=null,k=null,V=null;null===w.calculatedDuration&&s&&(w.calculatedDuration=e8(w));let{calculatedDuration:S}=w,M=1/0,E=1/0;null!==S&&(E=(M=S+o)*(s+1)-o);let C=0,D=t=>{if(null===k)return;v>0&&(k=Math.min(k,t)),v<0&&(k=Math.min(t-E/v,k));let i=(C=null!==A?A:Math.round(t-k)*v)-e*(v>=0?1:-1),r=v>=0?i<0:i>E;C=Math.max(i,0),"finished"===T&&null===A&&(C=E);let l=C,u=w;if(s){let t=Math.min(C,E)/M,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,s+1))%2&&("reverse"===a?(i=1-i,o&&(i-=o/M)):"mirror"===a&&(u=g)),l=Z(0,1,i)*M}let h=r?{done:!1,value:n[0]}:u.next(l);y&&(h.value=y(h.value));let{done:d}=h;r||null===S||(d=v>=0?C>=E:C<=0);let p=null===A&&("finished"===T||"running"===T&&d);return c&&c(h.value),p&&j(),h},L=()=>{f&&f.stop(),f=void 0},R=()=>{T="idle",L(),p(),P(),k=V=null},j=()=>{T="finished",h&&h(),L(),p()},F=()=>{if(x)return;f||(f=i(D));let t=f.now();l&&l(),null!==A?k=t-A:k&&"finished"!==T||(k=t),"finished"===T&&P(),V=k,A=null,T="running",f.start()};t&&F();let B={then:(t,e)=>m.then(t,e),get time(){return ee(C)},set time(newTime){C=newTime=et(newTime),null===A&&f&&0!==v?k=f.now()-newTime/v:A=newTime},get duration(){return ee(null===w.calculatedDuration?e8(w):w.calculatedDuration)},get speed(){return v},set speed(newSpeed){if(newSpeed===v||!f)return;v=newSpeed,B.time=ee(C)},get state(){return T},play:F,pause:()=>{T="paused",A=C},stop:()=>{x=!0,"idle"!==T&&(T="idle",u&&u(),R())},cancel:()=>{null!==V&&D(V),R()},complete:()=>{T="finished"},sample:t=>(k=0,D(t))};return B}let ii=(s=()=>Object.hasOwnProperty.call(Element.prototype,"animate"),()=>(void 0===n&&(n=s()),n)),ir=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),is=(t,e)=>"spring"===e.type||"backgroundColor"===t||!function t(e){return!!(!e||"string"==typeof e&&es[e]||en(e)||Array.isArray(e)&&e.every(t))}(e.ease),io={type:"spring",stiffness:500,damping:25,restSpeed:10},ia=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),il={type:"keyframes",duration:.8},iu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ih=(t,{keyframes:e})=>e.length>2?il:L.has(t)?t.startsWith("scale")?ia(e[1]):io:iu,ic=(t,e)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eH.test(e)||"0"===e)&&!e.startsWith("url(")),id=new Set(["brightness","contrast","saturate","opacity"]);function ip(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(q)||[];if(!n)return t;let r=i.replace(n,""),s=id.has(e)?1:0;return n!==i&&(s*=100),e+"("+s+r+")"}let im=/([a-z-]*)\(.*?\)/g,iy={...eH,getAnimatableNone:t=>{let e=t.match(im);return e?e.map(ip).join(" "):t}},ig={...tr,color:eE,backgroundColor:eE,outlineColor:eE,fill:eE,stroke:eE,borderColor:eE,borderTopColor:eE,borderRightColor:eE,borderBottomColor:eE,borderLeftColor:eE,filter:iy,WebkitFilter:iy},iv=t=>ig[t];function ix(t,e){let i=iv(t);return i!==iy&&(i=eH),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let iP=t=>/^0[^.\s]+$/.test(t);function ib(t,e){return t[e]||t.default||t}let iw={skipAnimations:!1},iT=(t,e,i,n={})=>r=>{let s=ib(n,t)||{},o=s.delay||n.delay||0,{elapsed:a=0}=n;a-=et(o);let l=function(t,e,i,n){let r,s;let o=ic(e,i);r=Array.isArray(i)?[...i]:[null,i];let a=void 0!==n.from?n.from:t.get(),l=[];for(let t=0;t<r.length;t++){var u;null===r[t]&&(r[t]=0===t?a:r[t-1]),("number"==typeof(u=r[t])?0===u:null!==u?"none"===u||"0"===u||iP(u):void 0)&&l.push(t),"string"==typeof r[t]&&"none"!==r[t]&&"0"!==r[t]&&(s=r[t])}if(o&&l.length&&s)for(let t=0;t<l.length;t++)r[l[t]]=ix(e,s);return r}(e,t,i,s),u=l[0],h=l[l.length-1],c=ic(t,u),d=ic(t,h);tM(c===d,`You are trying to animate ${t} from "${u}" to "${h}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${h} via the \`style\` property.`);let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:t=>{e.set(t),s.onUpdate&&s.onUpdate(t)},onComplete:()=>{r(),s.onComplete&&s.onComplete()}};if(!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(s)&&(p={...p,...ih(t,p)}),p.duration&&(p.duration=et(p.duration)),p.repeatDelay&&(p.repeatDelay=et(p.repeatDelay)),!c||!d||ei.current||!1===s.type||iw.skipAnimations)return function({keyframes:t,delay:e,onUpdate:i,onComplete:n}){let r=()=>(i&&i(t[t.length-1]),n&&n(),{time:0,speed:1,duration:0,play:tM,pause:tM,stop:tM,then:t=>(t(),Promise.resolve()),cancel:tM,complete:tM});return e?ie({keyframes:[0,1],duration:0,delay:e,onComplete:r}):r()}(ei.current?{...p,delay:0}:p);if(!n.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){let i=function(t,e,{onUpdate:i,onComplete:n,...r}){let s,o;if(!(ii()&&ir.has(e)&&!r.repeatDelay&&"mirror"!==r.repeatType&&0!==r.damping&&"inertia"!==r.type))return!1;let a=!1,l=!1,u=()=>{o=new Promise(t=>{s=t})};u();let{keyframes:h,duration:c=300,ease:d,times:p}=r;if(is(e,r)){let t=ie({...r,repeat:0,delay:0}),e={done:!1,value:h[0]},i=[],n=0;for(;!e.done&&n<2e4;)e=t.sample(n),i.push(e.value),n+=10;p=void 0,h=i,c=n-10,d="linear"}let m=function(t,e,i,{delay:n=0,duration:r,repeat:s=0,repeatType:o="loop",ease:a,times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e){if(e)return en(e)?er(e):Array.isArray(e)?e.map(t):es[e]}(a);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:n,duration:r,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(t.owner.current,e,h,{...r,duration:c,ease:d,times:p}),f=()=>{l=!1,m.cancel()},y=()=>{l=!0,tD.update(f),s(),u()};return m.onfinish=()=>{l||(t.set(function(t,{repeat:e,repeatType:i="loop"}){let n=e&&"loop"!==i&&e%2==1?0:t.length-1;return t[n]}(h,r)),n&&n(),y())},{then:(t,e)=>o.then(t,e),attachTimeline:t=>(m.timeline=t,m.onfinish=null,tM),get time(){return ee(m.currentTime||0)},set time(newTime){m.currentTime=et(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return ee(c)},play:()=>{a||(m.play(),tL(f))},pause:()=>m.pause(),stop:()=>{if(a=!0,"idle"===m.playState)return;let{currentTime:e}=m;if(e){let i=ie({...r,autoplay:!1});t.setWithVelocity(i.sample(e-10).value,i.sample(e).value,10)}y()},complete:()=>{l||m.finish()},cancel:y}}(e,t,p);if(i)return i}return ie(p)};function iA(t){return!!(j(t)&&t.add)}let ik=t=>/^\-?\d*\.?\d+$/.test(t);function iV(t,e){-1===t.indexOf(e)&&t.push(e)}function iS(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class iM{constructor(){this.subscriptions=[]}add(t){return iV(this.subscriptions,t),()=>iS(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let iE=t=>!isNaN(parseFloat(t)),iC={current:void 0};class iD{constructor(t,e={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(t,e=!0)=>{this.prev=this.current,this.current=t;let{delta:i,timestamp:n}=tR;this.lastUpdated!==n&&(this.timeDelta=i,this.lastUpdated=n,tD.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>tD.postRender(this.velocityCheck),this.velocityCheck=({timestamp:t})=>{t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=iE(this.current),this.owner=e.owner}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new iM);let i=this.events[t].add(e);return"change"===t?()=>{i(),tD.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=t,this.timeDelta=i}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return iC.current&&iC.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t,e;return this.canTrackVelocity?(t=parseFloat(this.current)-parseFloat(this.prev),(e=this.timeDelta)?1e3/e*t:0):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function iL(t,e){return new iD(t,e)}let iR=t=>e=>e.test(t),ij=[$,Q,J,_,te,tt,{test:t=>"auto"===t,parse:t=>t}],iF=t=>ij.find(iR(t)),iB=[...ij,eE,eH],iO=t=>iB.find(iR(t));function iU(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=t.makeTargetAnimatable(e),l=t.getValue("willChange");n&&(s=n);let u=[],h=r&&t.animationState&&t.animationState.getState()[r];for(let e in a){let n=t.getValue(e),r=a[e];if(!n||void 0===r||h&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(h,e))continue;let o={delay:i,elapsed:0,...ib(s||{},e)};if(window.HandoffAppearAnimations){let i=t.getProps()[m];if(i){let t=window.HandoffAppearAnimations(i,e,n,tD);null!==t&&(o.elapsed=t,o.isHandoff=!0)}}let c=!o.isHandoff&&!function(t,e){let i=t.get();if(!Array.isArray(e))return i!==e;for(let t=0;t<e.length;t++)if(e[t]!==i)return!0}(n,r);if("spring"===o.type&&(n.getVelocity()||o.velocity)&&(c=!1),n.animation&&(c=!1),c)continue;n.start(iT(e,n,r,t.shouldReduceMotion&&L.has(e)?{type:!1}:o));let d=n.animation;iA(l)&&(l.add(e),d.then(()=>l.remove(e))),u.push(d)}return o&&Promise.all(u).then(()=>{o&&function(t,e){let i=t8(t,e),{transitionEnd:n={},transition:r={},...s}=i?t.makeTargetAnimatable(i,!1):{};for(let e in s={...s,...n}){let i=tk(s[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,iL(i))}}(t,o)}),u}function iI(t,e,i={}){let n=t8(t,e,i.custom),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(iU(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(iN).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(iI(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+n,o,a,i)}:()=>Promise.resolve(),{when:a}=r;if(!a)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function iN(t,e){return t.sortNodePosition(e)}let iZ=[...v].reverse(),i$=v.length;function iW(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class iz extends tG{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>iI(t,e,i)));else if("string"==typeof e)n=iI(t,e,i);else{let r="function"==typeof e?t8(t,e,i.custom):e;n=Promise.all(iU(t,r,i))}return n.then(()=>t.notify("AnimationComplete",e))})(t,e,i))),i={animate:iW(!0),whileInView:iW(),whileHover:iW(),whileTap:iW(),whileDrag:iW(),whileFocus:iW(),exit:iW()},n=!0,r=(e,i)=>{let n=t8(t,i);if(n){let{transition:t,transitionEnd:i,...r}=n;e={...e,...r,...i}}return e};function s(s,o){let a=t.getProps(),l=t.getVariantContext(!0)||{},u=[],h=new Set,c={},d=1/0;for(let e=0;e<i$;e++){var p;let m=iZ[e],f=i[m],v=void 0!==a[m]?a[m]:l[m],x=y(v),P=m===o?f.isActive:null;!1===P&&(d=e);let b=v===l[m]&&v!==a[m]&&x;if(b&&n&&t.manuallyAnimateOnMount&&(b=!1),f.protectedKeys={...c},!f.isActive&&null===P||!v&&!f.prevProp||g(v)||"boolean"==typeof v)continue;let w=(p=f.prevProp,("string"==typeof v?v!==p:!!Array.isArray(v)&&!t7(v,p))||m===o&&f.isActive&&!b&&x||e>d&&x),T=!1,A=Array.isArray(v)?v:[v],k=A.reduce(r,{});!1===P&&(k={});let{prevResolvedValues:V={}}=f,S={...V,...k},M=t=>{w=!0,h.has(t)&&(T=!0,h.delete(t)),f.needsAnimating[t]=!0};for(let t in S){let e=k[t],i=V[t];if(!c.hasOwnProperty(t))(tT(e)&&tT(i)?t7(e,i):e===i)?void 0!==e&&h.has(t)?M(t):f.protectedKeys[t]=!0:void 0!==e?M(t):h.add(t)}f.prevProp=v,f.prevResolvedValues=k,f.isActive&&(c={...c,...k}),n&&t.blockInitialAnimation&&(w=!1),w&&(!b||T)&&u.push(...A.map(t=>({animation:t,options:{type:m,...s}})))}if(h.size){let e={};h.forEach(i=>{let n=t.getBaseTarget(i);void 0!==n&&(e[i]=n)}),u.push({animation:e})}let m=!!u.length;return n&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(m=!1),n=!1,m?e(u):Promise.resolve()}return{animateChanges:s,setActive:function(e,n,r){var o;if(i[e].isActive===n)return Promise.resolve();null===(o=t.variantChildren)||void 0===o||o.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,n)}),i[e].isActive=n;let a=s(r,e);for(let t in i)i[t].protectedKeys={};return a},setAnimateFunction:function(i){e=i(t)},getState:()=>i}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),g(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}let iH=0;class iq extends tG{constructor(){super(...arguments),this.id=iH++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e,custom:i}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;let r=this.node.animationState.setActive("exit",!t,{custom:null!=i?i:this.node.getProps().custom});e&&!t&&r.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let iY=(t,e)=>Math.abs(t-e);class iX{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let i=i_(this.lastMoveEventInfo,this.history),n=null!==this.startEvent,r=(t=i.offset,e={x:0,y:0},Math.sqrt(iY(t.x,e.x)**2+iY(t.y,e.y)**2)>=3);if(!n&&!r)return;let{point:s}=i,{timestamp:o}=tR;this.history.push({...s,timestamp:o});let{onStart:a,onMove:l}=this.handlers;n||(a&&a(this.lastMoveEvent,i),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,i)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iG(e,this.transformPagePoint),tD.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=i_("pointercancel"===t.type?this.lastMoveEventInfo:iG(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!tU(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=iG(tI(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=tR;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,i_(s,this.history)),this.removeListeners=tW(tZ(this.contextWindow,"pointermove",this.handlePointerMove),tZ(this.contextWindow,"pointerup",this.handlePointerUp),tZ(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),tL(this.updatePoint)}}function iG(t,e){return e?{point:e(t.point)}:t}function iK(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i_({point:t},e){return{point:t,delta:iK(t,iJ(e)),offset:iK(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iJ(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>et(.1)));)i--;if(!n)return{x:0,y:0};let s=ee(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function iJ(t){return t[t.length-1]}function iQ(t){return t.max-t.min}function i0(t,e=0,i=.01){return Math.abs(t-e)<=i}function i1(t,e,i,n=.5){t.origin=n,t.originPoint=eC(e.min,e.max,t.origin),t.scale=iQ(i)/iQ(e),(i0(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=eC(i.min,i.max,t.origin)-t.originPoint,(i0(t.translate)||isNaN(t.translate))&&(t.translate=0)}function i2(t,e,i,n){i1(t.x,e.x,i.x,n?n.originX:void 0),i1(t.y,e.y,i.y,n?n.originY:void 0)}function i5(t,e,i){t.min=i.min+e.min,t.max=t.min+iQ(e)}function i3(t,e,i){t.min=e.min-i.min,t.max=t.min+iQ(e)}function i6(t,e,i){i3(t.x,e.x,i.x),i3(t.y,e.y,i.y)}function i9(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i4(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i7(t,e,i){return{min:i8(t,e),max:i8(t,i)}}function i8(t,e){return"number"==typeof t?t:t[e]||0}let nt=()=>({translate:0,scale:1,origin:0,originPoint:0}),ne=()=>({x:nt(),y:nt()}),ni=()=>({min:0,max:0}),nn=()=>({x:ni(),y:ni()});function nr(t){return[t("x"),t("y")]}function ns({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function no(t){return void 0===t||1===t}function na({scale:t,scaleX:e,scaleY:i}){return!no(t)||!no(e)||!no(i)}function nl(t){return na(t)||nu(t)||t.z||t.rotate||t.rotateX||t.rotateY}function nu(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function nh(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function nc(t,e=0,i=1,n,r){t.min=nh(t.min,e,i,n,r),t.max=nh(t.max,e,i,n,r)}function nd(t,{x:e,y:i}){nc(t.x,e.translate,e.scale,e.originPoint),nc(t.y,i.translate,i.scale,i.originPoint)}function np(t){return Number.isInteger(t)?t:t>1.0000000000001||t<.999999999999?t:1}function nm(t,e){t.min=t.min+e,t.max=t.max+e}function nf(t,e,[i,n,r]){let s=void 0!==e[r]?e[r]:.5,o=eC(t.min,t.max,s);nc(t,e[i],e[n],o,e.scale)}let ny=["x","scaleX","originX"],ng=["y","scaleY","originY"];function nv(t,e){nf(t.x,e,ny),nf(t.y,e,ng)}function nx(t,e){return ns(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let nP=({current:t})=>t?t.ownerDocument.defaultView:null,nb=new WeakMap;class nw{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nn(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iX(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tI(t,"page").point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=tY(i),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nr(t=>{let e=this.getAxisMotionValue(t).get()||0;if(J.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];if(n){let t=iQ(n);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),r&&tD.update(()=>r(t,e),!1,!0);let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openGlobalLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>nr(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:nP(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&tD.update(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!nT(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?eC(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?eC(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,r=this.constraints;e&&f(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:i9(t.x,i,r),y:i9(t.y,e,n)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i7(t,"left","right"),y:i7(t,"top","bottom")}}(i),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nr(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!f(e))return!1;let n=e.current;tM(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=nx(t,i),{scroll:r}=e;return r&&(nm(n.x,r.offset.x),nm(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o={x:i4((t=r.layout.layoutBox).x,s.x),y:i4(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ns(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(nr(o=>{if(!nT(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return i.start(iT(t,i,0,e))}stopAnimation(){nr(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){nr(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e="_drag"+t.toUpperCase(),i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){nr(e=>{let{drag:i}=this.getProps();if(!nT(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-eC(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!f(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};nr(t=>{let e=this.getAxisMotionValue(t);if(e){let i=e.get();n[t]=function(t,e){let i=.5,n=iQ(t),r=iQ(e);return r>n?i=e_(e.min,e.max-n,t.min):n>r&&(i=e_(t.min,t.max-r,e.min)),Z(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),nr(e=>{if(!nT(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(eC(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;nb.set(this.visualElement,this);let t=tZ(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();f(t)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),e();let r=tO(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(nr(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function nT(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class nA extends tG{constructor(t){super(t),this.removeGroupControls=tM,this.removeListeners=tM,this.controls=new nw(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tM}unmount(){this.removeGroupControls(),this.removeListeners()}}let nk=t=>(e,i)=>{t&&tD.update(()=>t(e,i))};class nV extends tG{constructor(){super(...arguments),this.removePointerDownListener=tM}onPointerDown(t){this.session=new iX(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nP(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:nk(t),onStart:nk(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&tD.update(()=>n(t,e))}}}mount(){this.removePointerDownListener=tZ(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let nS={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nM(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let nE={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Q.test(t))return t;t=parseFloat(t)}let i=nM(t,e.target.x),n=nM(t,e.target.y);return`${i}% ${n}%`}};class nC extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;Object.assign(C,nL),r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nS.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,s=i.projection;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent===r||(r?s.promote():s.relegate()||tD.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nD(t){let[e,i]=function(){let t=(0,o.useContext)(u);if(null===t)return[!0,null];let{isPresent:e,onExitComplete:i,register:n}=t,r=(0,o.useId)();return(0,o.useEffect)(()=>n(r),[]),!e&&i?[!1,()=>i&&i(r)]:[!0]}(),n=(0,o.useContext)(k);return o.createElement(nC,{...t,layoutGroup:n,switchLayoutGroup:(0,o.useContext)(V),isPresent:e,safeToRemove:i})}let nL={borderRadius:{...nE,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nE,borderTopRightRadius:nE,borderBottomLeftRadius:nE,borderBottomRightRadius:nE,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=eH.parse(t);if(n.length>5)return t;let r=eH.createTransformer(t),s="number"!=typeof n[0]?1:0,o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=eC(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}},nR=["TopLeft","TopRight","BottomLeft","BottomRight"],nj=nR.length,nF=t=>"string"==typeof t?parseFloat(t):t,nB=t=>"number"==typeof t||Q.test(t);function nO(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nU=nN(0,.5,ef),nI=nN(.5,.95,tM);function nN(t,e,i){return n=>n<t?0:n>e?1:i(e_(t,e,n))}function nZ(t,e){t.min=e.min,t.max=e.max}function n$(t,e){nZ(t.x,e.x),nZ(t.y,e.y)}function nW(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nz(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(J.test(e)&&(e=parseFloat(e),e=eC(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eC(s.min,s.max,n);t===s&&(a-=e),t.min=nW(t.min,e,i,a,r),t.max=nW(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nH=["x","scaleX","originX"],nq=["y","scaleY","originY"];function nY(t,e,i,n){nz(t.x,e,nH,i?i.x:void 0,n?n.x:void 0),nz(t.y,e,nq,i?i.y:void 0,n?n.y:void 0)}function nX(t){return 0===t.translate&&1===t.scale}function nG(t){return nX(t.x)&&nX(t.y)}function nK(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function n_(t){return iQ(t.x)/iQ(t.y)}class nJ{constructor(){this.members=[]}add(t){iV(this.members,t),t.scheduleRender()}remove(t){if(iS(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function nQ(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y;if((r||s)&&(n=`translate3d(${r}px, ${s}px, 0) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{rotate:t,rotateX:e,rotateY:r}=i;t&&(n+=`rotate(${t}deg) `),e&&(n+=`rotateX(${e}deg) `),r&&(n+=`rotateY(${r}deg) `)}let o=t.x.scale*e.x,a=t.y.scale*e.y;return(1!==o||1!==a)&&(n+=`scale(${o}, ${a})`),n||"none"}let n0=(t,e)=>t.depth-e.depth;class n1{constructor(){this.children=[],this.isDirty=!1}add(t){iV(this.children,t),this.isDirty=!0}remove(t){iS(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(n0),this.isDirty=!1,this.children.forEach(t)}}let n2=["","X","Y","Z"],n5={visibility:"hidden"},n3=0,n6={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function n9({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=null==e?void 0:e()){this.id=n3++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,n6.totalNodes=n6.resolvedTargetDeltas=n6.recalculatedProjection=0,this.nodes.forEach(n8),this.nodes.forEach(ro),this.nodes.forEach(ra),this.nodes.forEach(rt),window.MotionDebug&&window.MotionDebug.record(n6)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new n1)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new iM),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:n,layout:r,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(r||n)&&(this.isLayoutDirty=!0),t){let i;let n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=performance.now(),n=({timestamp:e})=>{let r=e-i;r>=250&&(tL(n),t(r-250))};return tD.read(n,!0),()=>tL(n)}(n,0),nS.hasAnimatedSinceResize&&(nS.hasAnimatedSinceResize=!1,this.nodes.forEach(rs))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||rp,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!nK(this.targetLayout,n)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...ib(r,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||rs(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,tL(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rl),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ri);return}this.isUpdating||this.nodes.forEach(rn),this.isUpdating=!1,this.nodes.forEach(rr),this.nodes.forEach(n4),this.nodes.forEach(n7),this.clearAllSnapshots();let t=performance.now();tR.delta=Z(0,1e3/60,t-tR.timestamp),tR.timestamp=t,tR.isProcessing=!0,tj.update.process(tR),tj.preRender.process(tR),tj.render.process(tR),tR.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(re),this.sharedNodes.forEach(ru)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tD.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tD.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nn(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:n(this.instance),offset:i(this.instance)})}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!nG(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&(e||nl(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),ry((e=n).x),ry(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return nn();let e=t.measureViewportBox(),{scroll:i}=this.root;return i&&(nm(e.x,i.offset.x),nm(e.y,i.offset.y)),e}removeElementScroll(t){let e=nn();n$(e,t);for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;if(n!==this.root&&r&&s.layoutScroll){if(r.isRoot){n$(e,t);let{scroll:i}=this.root;i&&(nm(e.x,-i.offset.x),nm(e.y,-i.offset.y))}nm(e.x,r.offset.x),nm(e.y,r.offset.y)}}return e}applyTransform(t,e=!1){let i=nn();n$(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&nv(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),nl(n.latestValues)&&nv(i,n.latestValues)}return nl(this.latestValues)&&nv(i,this.latestValues),i}removeTransform(t){let e=nn();n$(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!nl(i.latestValues))continue;na(i.latestValues)&&i.updateSnapshot();let n=nn();n$(n,i.measurePageBox()),nY(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return nl(this.latestValues)&&nY(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tR.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,n,r;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=tR.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nn(),this.relativeTargetOrigin=nn(),i6(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),n$(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=nn(),this.targetWithTransforms=nn()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,n=this.relativeTarget,r=this.relativeParent.target,i5(i.x,n.x,r.x),i5(i.y,n.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):n$(this.target,this.layout.layoutBox),nd(this.target,this.targetDelta)):n$(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nn(),this.relativeTargetOrigin=nn(),i6(this.relativeTargetOrigin,this.target,t.target),n$(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}n6.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||na(this.parent.latestValues)||nu(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(n=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===tR.timestamp&&(n=!1),n)return;let{layout:r,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||s))return;n$(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let r,s;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let o=r.instance;(!o||!o.style||"contents"!==o.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nv(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,nd(t,s)),n&&nl(r.latestValues)&&nv(t,r.latestValues))}e.x=np(e.x),e.y=np(e.y)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox);let{target:l}=e;if(!l){this.projectionTransform&&(this.projectionDelta=ne(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=ne(),this.projectionDeltaWithTransform=ne());let u=this.projectionTransform;i2(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=nQ(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==o||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),n6.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){let i;let n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=ne();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=nn(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(rd));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(rh(o.x,t.x,n),rh(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m;i6(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,rc(p.x,m.x,a.x,n),rc(p.y,m.y,a.y,n),i&&(u=this.relativeTarget,d=i,u.x.min===d.x.min&&u.x.max===d.x.max&&u.y.min===d.y.min&&u.y.max===d.y.max)&&(this.isProjectionDirty=!1),i||(i=nn()),n$(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=eC(0,void 0!==i.opacity?i.opacity:1,nU(n)),t.opacityExit=eC(void 0!==e.opacity?e.opacity:1,0,nI(n))):s&&(t.opacity=eC(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,n));for(let r=0;r<nj;r++){let s=`border${nR[r]}Radius`,o=nO(e,s),a=nO(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nB(o)===nB(a)?(t[s]=Math.max(eC(nF(o),nF(a),n),0),(J.test(a)||J.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=eC(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(tL(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tD.update(()=>{nS.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let n=j(0)?0:iL(0);return n.start(iT("",n,1e3,i)),n.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&rg(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||nn();let e=iQ(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iQ(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}n$(e,i),nv(e,r),i2(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nJ),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.rotate||i.rotateX||i.rotateY||i.rotateZ)&&(e=!0),!e)return;let n={};for(let e=0;e<n2.length;e++){let r="rotate"+n2[e];i[r]&&(n[r]=i[r],t.setStaticValue(r,0))}for(let e in t.render(),n)t.setStaticValue(e,n[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return n5;let n={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=tV(null==t?void 0:t.pointerEvents)||"",n.transform=r?r(this.latestValues,""):"none",n;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tV(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!nl(this.latestValues)&&(e.transform=r?r({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),n.transform=nQ(this.projectionDeltaWithTransform,this.treeScale,o),r&&(n.transform=r(o,n.transform));let{x:a,y:l}=this.projectionDelta;for(let t in n.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?n.opacity=s===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:n.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,C){if(void 0===o[t])continue;let{correct:e,applyTo:i}=C[t],r="none"===n.transform?o[t]:e(o[t],s);if(i){let t=i.length;for(let e=0;e<t;e++)n[i[e]]=r}else n[t]=r}return this.options.layoutId&&(n.pointerEvents=s===this?tV(null==t?void 0:t.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(ri),this.root.sharedNodes.clear()}}}function n4(t){t.updateLayout()}function n7(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:r}=t.options,s=i.source!==t.layout.source;"size"===r?nr(t=>{let n=s?i.measuredBox[t]:i.layoutBox[t],r=iQ(n);n.min=e[t].min,n.max=n.min+r}):rg(r,i.layoutBox,e)&&nr(n=>{let r=s?i.measuredBox[n]:i.layoutBox[n],o=iQ(e[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=ne();i2(o,e,i.layoutBox);let a=ne();s?i2(a,t.applyTransform(n,!0),i.measuredBox):i2(a,e,i.layoutBox);let l=!nG(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=nn();i6(o,i.layoutBox,r.layoutBox);let a=nn();i6(a,e,s.layoutBox),nK(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function n8(t){n6.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rt(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function re(t){t.clearSnapshot()}function ri(t){t.clearMeasurements()}function rn(t){t.isLayoutDirty=!1}function rr(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function rs(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ro(t){t.resolveTargetDelta()}function ra(t){t.calcProjection()}function rl(t){t.resetRotation()}function ru(t){t.removeLeadSnapshot()}function rh(t,e,i){t.translate=eC(e.translate,0,i),t.scale=eC(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function rc(t,e,i,n){t.min=eC(e.min,i.min,n),t.max=eC(e.max,i.max,n)}function rd(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let rp={duration:.45,ease:[.4,0,.1,1]},rm=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),rf=rm("applewebkit/")&&!rm("chrome/")?Math.round:tM;function ry(t){t.min=rf(t.min),t.max=rf(t.max)}function rg(t,e,i){return"position"===t||"preserve-aspect"===t&&!i0(n_(e),n_(i),.2)}let rv=n9({attachResizeListener:(t,e)=>tO(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rx={current:void 0},rP=n9({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rx.current){let t=new rv({});t.mount(window),t.setOptions({layoutScroll:!0}),rx.current=t}return rx.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position}),rb=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function rw(t,e,i=1){tM(i<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=rb.exec(t);if(!e)return[,];let[,i,n]=e;return[i,n]}(t);if(!n)return;let s=window.getComputedStyle(e).getPropertyValue(n);if(s){let t=s.trim();return ik(t)?parseFloat(t):t}return I(r)?rw(r,e,i+1):r}let rT=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),rA=t=>rT.has(t),rk=t=>Object.keys(t).some(rA),rV=t=>t===$||t===Q,rS=(t,e)=>parseFloat(t.split(", ")[e]),rM=(t,e)=>(i,{transform:n})=>{if("none"===n||!n)return 0;let r=n.match(/^matrix3d\((.+)\)$/);if(r)return rS(r[1],e);{let e=n.match(/^matrix\((.+)\)$/);return e?rS(e[1],t):0}},rE=new Set(["x","y","z"]),rC=D.filter(t=>!rE.has(t)),rD={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:rM(4,13),y:rM(5,14)};rD.translateX=rD.x,rD.translateY=rD.y;let rL=(t,e,i)=>{let n=e.measureViewportBox(),r=getComputedStyle(e.current),{display:s}=r,o={};"none"===s&&e.setStaticValue("display",t.display||"block"),i.forEach(t=>{o[t]=rD[t](n,r)}),e.render();let a=e.measureViewportBox();return i.forEach(i=>{let n=e.getValue(i);n&&n.jump(o[i]),t[i]=rD[i](a,r)}),t},rR=(t,e,i={},n={})=>{e={...e},n={...n};let r=Object.keys(e).filter(rA),s=[],o=!1,a=[];if(r.forEach(r=>{let l;let u=t.getValue(r);if(!t.hasValue(r))return;let h=i[r],c=iF(h),d=e[r];if(tT(d)){let t=d.length,e=null===d[0]?1:0;c=iF(h=d[e]);for(let i=e;i<t&&null!==d[i];i++)l?tM(iF(d[i])===l,"All keyframes must be of the same type"):tM((l=iF(d[i]))===c||rV(c)&&rV(l),"Keyframes must be of the same dimension as the current value")}else l=iF(d);if(c!==l){if(rV(c)&&rV(l)){let t=u.get();"string"==typeof t&&u.set(parseFloat(t)),"string"==typeof d?e[r]=parseFloat(d):Array.isArray(d)&&l===Q&&(e[r]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===h||0===d)?0===h?u.set(l.transform(h)):e[r]=c.transform(d):(o||(s=function(t){let e=[];return rC.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),o=!0),a.push(r),n[r]=void 0!==n[r]?n[r]:e[r],u.jump(d))}}),!a.length)return{target:e,transitionEnd:n};{let i=a.indexOf("height")>=0?window.pageYOffset:null,r=rL(e,t,a);return s.length&&s.forEach(([e,i])=>{t.getValue(e).set(i)}),t.render(),h&&null!==i&&window.scrollTo({top:i}),{target:r,transitionEnd:n}}},rj=(t,e,i,n)=>{var r,s;let o=function(t,{...e},i){let n=t.current;if(!(n instanceof Element))return{target:e,transitionEnd:i};for(let r in i&&(i={...i}),t.values.forEach(t=>{let e=t.get();if(!I(e))return;let i=rw(e,n);i&&t.set(i)}),e){let t=e[r];if(!I(t))continue;let s=rw(t,n);s&&(e[r]=s,i||(i={}),void 0===i[r]&&(i[r]=t))}return{target:e,transitionEnd:i}}(t,e,n);return e=o.target,n=o.transitionEnd,r=e,s=n,rk(r)?rR(t,r,i,s):{target:r,transitionEnd:s}},rF={current:null},rB={current:!1},rO=new WeakMap,rU=Object.keys(A),rI=rU.length,rN=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],rZ=x.length;class r${constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,visualState:r},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>tD.render(this.render,!1,!0);let{latestValues:o,renderState:a}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=s,this.isControllingVariants=P(e),this.isVariantNode=b(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{});for(let t in u){let e=u[t];void 0!==o[t]&&j(e)&&(e.set(o[t],!1),iA(l)&&l.add(t))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,rO.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),rB.current||function(){if(rB.current=!0,h){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>rF.current=t.matches;t.addListener(e),e()}else rF.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rF.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in rO.delete(this.current),this.projection&&this.projection.unmount(),tL(this.notifyUpdate),tL(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){let i=L.has(t),n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tD.update(this.notifyUpdate,!1,!0),i&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{n(),r()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},i,n,r){let s,o;for(let t=0;t<rI;t++){let i=rU[t],{isEnabled:n,Feature:r,ProjectionNode:a,MeasureLayout:l}=A[i];a&&(s=a),n(e)&&(!this.features[i]&&r&&(this.features[i]=new r(this)),l&&(o=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);let{layoutId:t,layout:i,drag:n,dragConstraints:o,layoutScroll:a,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:i,alwaysMeasureLayout:!!n||o&&f(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof i?i:"both",initialPromotionConfig:r,layoutScroll:a,layoutRoot:l})}return o}updateFeatures(){for(let t in this.features){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nn()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rN.length;e++){let i=rN[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){let{willChange:n}=e;for(let r in e){let s=e[r],o=i[r];if(j(s))t.addValue(r,s),iA(n)&&n.add(r);else if(j(o))t.addValue(r,iL(s,{owner:t})),iA(n)&&n.remove(r);else if(o!==s){if(t.hasValue(r)){let e=t.getValue(r);e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(r);t.addValue(r,iL(void 0!==e?e:s,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}let e={};for(let t=0;t<rZ;t++){let i=x[t],n=this.props[i];(y(n)||!1===n)&&(e[i]=n)}return e}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=iL(e,{owner:this}),this.addValue(t,i)),i}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let{initial:i}=this.props,n="string"==typeof i||"object"==typeof i?null===(e=tw(this.props,i))||void 0===e?void 0:e[t]:void 0;if(i&&void 0!==n)return n;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||j(r)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new iM),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rW extends r${sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...i},{transformValues:n},r){let s=function(t,e,i){let n={};for(let r in t){let t=function(t,e){if(e)return(e[t]||e.default||e).from}(r,e);if(void 0!==t)n[r]=t;else{let t=i.getValue(r);t&&(n[r]=t.get())}}return n}(i,t||{},this);if(n&&(e&&(e=n(e)),i&&(i=n(i)),s&&(s=n(s))),r){!function(t,e,i){var n,r;let s=Object.keys(e).filter(e=>!t.hasValue(e)),o=s.length;if(o)for(let a=0;a<o;a++){let o=s[a],l=e[o],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(r=null!==(n=i[o])&&void 0!==n?n:t.readValue(o))&&void 0!==r?r:e[o]),null!=u&&("string"==typeof u&&(ik(u)||iP(u))?u=parseFloat(u):!iO(u)&&eH.test(l)&&(u=ix(o,l)),t.addValue(o,iL(u,{owner:t})),void 0===i[o]&&(i[o]=u),null!==u&&t.setBaseTarget(o,u))}}(this,i,s);let t=rj(this,i,s,e);e=t.transitionEnd,i=t.target}return{transition:t,transitionEnd:e,...i}}}class rz extends rW{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(L.has(e)){let t=iv(e);return t&&t.default||0}{let i=window.getComputedStyle(t),n=(U(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return nx(t,e)}build(t,e,i,n){ts(t,e,i,n.transformTemplate)}scrapeMotionValuesFromProps(t,e){return tP(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;j(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(t,e,i,n){tg(t,e,i,n)}}class rH extends rW{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(L.has(e)){let t=iv(e);return t&&t.default||0}return e=tv.has(e)?e:p(e),t.getAttribute(e)}measureInstanceViewportBox(){return nn()}scrapeMotionValuesFromProps(t,e){return tb(t,e)}build(t,e,i,n){tm(t,e,i,this.isSVGTag,n.transformTemplate)}renderInstance(t,e,i,n){tx(t,e,i,n)}mount(t){this.isSVGTag=ty(t.tagName),super.mount(t)}}let rq=(t,e)=>E(t)?new rH(e,{enableHardwareAcceleration:!1}):new rz(e,{enableHardwareAcceleration:!0}),rY={animation:{Feature:iz},exit:{Feature:iq},inView:{Feature:t4},tap:{Feature:t1},focus:{Feature:tJ},hover:{Feature:t_},pan:{Feature:nV},drag:{Feature:nA,ProjectionNode:rP,MeasureLayout:nD},layout:{ProjectionNode:rP,MeasureLayout:nD}},rX=function(t){function e(e,i={}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:n,Component:r}){t&&function(t){for(let e in t)A[e]={...A[e],...t[e]}}(t);let s=(0,o.forwardRef)(function(s,p){var g;let v;let x={...(0,o.useContext)(a),...s,layoutId:function({layoutId:t}){let e=(0,o.useContext)(k).id;return e&&void 0!==t?e+"-"+t:t}(s)},{isStatic:b}=x,T=function(t){let{initial:e,animate:i}=function(t,e){if(P(t)){let{initial:e,animate:i}=t;return{initial:!1===e||y(e)?e:void 0,animate:y(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,o.useContext)(l));return(0,o.useMemo)(()=>({initial:e,animate:i}),[w(e),w(i)])}(s),A=n(s,b);if(!b&&h){T.visualElement=function(t,e,i,n){let{visualElement:r}=(0,o.useContext)(l),s=(0,o.useContext)(d),h=(0,o.useContext)(u),p=(0,o.useContext)(a).reducedMotion,f=(0,o.useRef)();n=n||s.renderer,!f.current&&n&&(f.current=n(t,{visualState:e,parent:r,props:i,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:p}));let y=f.current;(0,o.useInsertionEffect)(()=>{y&&y.update(i,h)});let g=(0,o.useRef)(!!(i[m]&&!window.HandoffComplete));return c(()=>{y&&(y.render(),g.current&&y.animationState&&y.animationState.animateChanges())}),(0,o.useEffect)(()=>{y&&(y.updateFeatures(),!g.current&&y.animationState&&y.animationState.animateChanges(),g.current&&(g.current=!1,window.HandoffComplete=!0))}),y}(r,A,x,e);let i=(0,o.useContext)(V),n=(0,o.useContext)(d).strict;T.visualElement&&(v=T.visualElement.loadFeatures(x,n,t,i))}return o.createElement(l.Provider,{value:T},v&&T.visualElement?o.createElement(v,{visualElement:T.visualElement,...x}):null,i(r,s,(g=T.visualElement,(0,o.useCallback)(t=>{t&&A.mount&&A.mount(t),g&&(t?g.mount(t):g.unmount()),p&&("function"==typeof p?p(t):f(p)&&(p.current=t))},[g])),A,b,T.visualElement))});return s[S]=r,s}(t(e,i))}if("undefined"==typeof Proxy)return e;let i=new Map;return new Proxy(e,{get:(t,n)=>(i.has(n)||i.set(n,e(n)),i.get(n))})}((t,e)=>(function(t,{forwardMotionProps:e=!1},i,n){return{...E(t)?tF:tB,preloadedFeatures:i,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let a=(E(e)?function(t,e,i,n){let r=(0,o.useMemo)(()=>{let i=tf();return tm(i,e,{enableHardwareAcceleration:!1},ty(n),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};ta(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e,i){let n={},r=function(t,e,i){let n=t.style||{},r={};return ta(r,n,t),Object.assign(r,function({transformTemplate:t},e,i){return(0,o.useMemo)(()=>{let n=to();return ts(n,e,{enableHardwareAcceleration:!i},t),Object.assign({},n.vars,n.style)},[e])}(t,e,i)),t.transformValues?t.transformValues(r):r}(t,e,i);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=r,n})(i,r,s,e),l={...function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(th(r)||!0===i&&tu(r)||!e&&!tu(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),...a,ref:n},{children:u}=i,h=(0,o.useMemo)(()=>j(u)?u.get():u,[u]);return(0,o.createElement)(e,{...l,children:h})}}(e),createVisualElement:n,Component:t}})(t,e,rY,rq))}}]);