{"name": "krtr-mesh-landing", "version": "1.0.0", "description": "Landing page for KRTR Mesh - Decentralized, encrypted, offline-first messaging", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.0", "lucide-react": "^0.292.0", "@next/font": "^14.0.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "eslint": "^8.52.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.2.0"}, "keywords": ["mesh networking", "decentralized messaging", "privacy", "encryption", "zero-knowledge", "offline-first", "bluetooth"], "author": "KRTR Team", "license": "MIT"}