# KRTR Mesh Landing Page - Deployment Guide

This guide will help you set up automatic deployment of the KRTR Mesh landing page to Vercel using GitHub Actions.

## 🚀 Quick Setup

### 1. Vercel Account Setup

1. Sign up for a [Vercel account](https://vercel.com) if you don't have one
2. Connect your GitHub account to Vercel
3. Import your repository to create a new Vercel project

### 2. Get Vercel Credentials

You'll need three pieces of information from Vercel:

#### Vercel Token
1. Go to [Vercel Account Settings](https://vercel.com/account/tokens)
2. Create a new token with a descriptive name (e.g., "KRTR Mesh GitHub Actions")
3. Copy the token value

#### Organization ID
1. Go to your [Vercel Team Settings](https://vercel.com/teams)
2. Copy your Team ID (or Personal Account ID if using personal account)

#### Project ID
1. Go to your project in Vercel dashboard
2. Go to Settings → General
3. Copy the Project ID

### 3. Configure GitHub Secrets

Add the following secrets to your GitHub repository:

1. Go to your GitHub repository
2. Navigate to Settings → Secrets and variables → Actions
3. Add these repository secrets:

```
VERCEL_TOKEN=your_vercel_token_here
VERCEL_ORG_ID=your_organization_id_here
VERCEL_PROJECT_ID=your_project_id_here
```

### 4. Configure Vercel Project

In your Vercel project settings:

1. **Framework Preset**: Next.js
2. **Root Directory**: `landing-page`
3. **Build Command**: `npm run build`
4. **Output Directory**: `out`
5. **Install Command**: `npm install`

### 5. Environment Variables (Optional)

If you need environment variables for your landing page:

1. Go to Vercel Project Settings → Environment Variables
2. Add any required variables for both Production and Preview environments

## 🔄 Deployment Workflow

### Automatic Deployments

The GitHub Actions workflow will automatically:

1. **On Push to Main**: Deploy to production
2. **On Pull Request**: Create preview deployment
3. **Run Tests**: Type checking and linting before deployment

### Manual Deployment

You can also deploy manually using Vercel CLI:

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy from landing-page directory
cd landing-page
vercel --prod
```

## 🛠️ Troubleshooting

### Common Issues

#### Build Failures
- Check that all dependencies are listed in `package.json`
- Ensure TypeScript types are correct
- Verify all imports are valid

#### Deployment Errors
- Verify Vercel secrets are correctly set in GitHub
- Check that the root directory is set to `landing-page`
- Ensure build command produces output in `out` directory

#### Preview Deployments Not Working
- Check that the workflow has permissions to comment on PRs
- Verify the Vercel token has appropriate permissions

### Debug Steps

1. **Check GitHub Actions logs**:
   - Go to Actions tab in your GitHub repository
   - Click on the failed workflow run
   - Review the logs for specific error messages

2. **Check Vercel deployment logs**:
   - Go to your Vercel project dashboard
   - Click on the failed deployment
   - Review the build and runtime logs

3. **Local testing**:
   ```bash
   cd landing-page
   npm install
   npm run build
   npm start
   ```

## 📊 Performance Optimization

### Vercel Analytics (Optional)

Enable Vercel Analytics for performance insights:

1. Go to your Vercel project
2. Navigate to Analytics tab
3. Enable Web Analytics
4. Add the analytics script to your layout if needed

### Custom Domain (Optional)

To use a custom domain:

1. Go to Vercel Project Settings → Domains
2. Add your custom domain
3. Configure DNS records as instructed
4. Update any hardcoded URLs in your application

## 🔒 Security Considerations

### Environment Variables
- Never commit sensitive data to the repository
- Use Vercel environment variables for any secrets
- Different values for production and preview environments

### Content Security Policy
The deployment includes security headers in `vercel.json`. Adjust as needed for your requirements.

## 📈 Monitoring

### Deployment Status
- GitHub Actions will show deployment status
- Vercel dashboard shows deployment history
- Set up notifications for failed deployments

### Performance Monitoring
- Use Vercel Analytics for performance insights
- Monitor Core Web Vitals
- Set up alerts for performance regressions

## 🆘 Support

If you encounter issues:

1. Check this troubleshooting guide
2. Review Vercel documentation
3. Check GitHub Actions documentation
4. Open an issue in the repository

## 📚 Additional Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
