//
// AudioService.swift
// KRTR
//
// This is free and unencumbered software released into the public domain.
// For more information, see <https://unlicense.org>
//

import Foundation
import AVFoundation
import SwiftUI

class AudioService: NSObject, ObservableObject {
    private var audioPlayer: AVAudioPlayer?
    private var audioSession: AVAudioSession
    
    @Published var isPlaying = false
    @Published var volume: Float = 0.7
    
    override init() {
        audioSession = AVAudioSession.sharedInstance()
        super.init()
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.ambient, mode: .default, options: [.mixWithOthers])
            try audioSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    func playCicadaMurmuration() {
        // Use generated cicada sound for now - creates a realistic cicada murmuration
        playGeneratedCicadaSound()
    }
    
    private func playGeneratedCicadaSound() {
        // Generate a synthesized cicada-like sound using Core Audio
        generateCicadaLikeSound()
    }
    
    private func generateCicadaLikeSound() {
        // Create a realistic cicada murmuration sound with multiple layers
        let sampleRate: Double = 44100
        let duration: Double = 3.0

        // Multiple cicada frequencies for murmuration effect
        let cicadaFrequencies: [Double] = [
            3800, 4200, 4600, 5000, 5400, // Main cicada buzz frequencies
            2800, 3200, 3600, // Lower harmonics
            6000, 6400, 6800  // Higher harmonics
        ]

        let frameCount = UInt32(sampleRate * duration)

        guard let audioFormat = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 1) else {
            print("Failed to create audio format")
            return
        }

        guard let audioBuffer = AVAudioPCMBuffer(pcmFormat: audioFormat, frameCapacity: frameCount) else {
            print("Failed to create audio buffer")
            return
        }

        audioBuffer.frameLength = frameCount
        let samples = audioBuffer.floatChannelData![0]

        for i in 0..<Int(frameCount) {
            let time = Double(i) / sampleRate
            var combinedSignal: Double = 0.0

            // Generate multiple cicada voices for murmuration effect
            for (index, frequency) in cicadaFrequencies.enumerated() {
                let phaseOffset = Double(index) * 0.3 // Slight phase differences
                let amplitudeVariation = 0.7 + 0.3 * sin(2.0 * .pi * Double(index) * 0.1 * time)

                // Individual cicada buzz with characteristic modulation
                let buzzModulation = sin(2.0 * .pi * (25.0 + Double(index) * 2.0) * time) * 0.4 + 0.6
                let cicadaWave = sin(2.0 * .pi * frequency * time + phaseOffset) * buzzModulation * amplitudeVariation

                // Weight different frequencies
                let weight = index < 5 ? 0.15 : (index < 8 ? 0.08 : 0.05)
                combinedSignal += cicadaWave * weight
            }

            // Add some natural randomness and texture
            let noise = (Double.random(in: -1...1) * 0.02) // Very subtle noise
            combinedSignal += noise

            // Overall amplitude modulation for swelling effect
            let swellModulation = sin(2.0 * .pi * 0.8 * time) * 0.2 + 0.8
            combinedSignal *= swellModulation

            // Apply realistic envelope (gradual fade in, sustain, gradual fade out)
            let envelope: Float
            if time < 0.3 {
                envelope = Float(time / 0.3) // Gradual fade in
            } else if time > duration - 0.8 {
                envelope = Float((duration - time) / 0.8) // Gradual fade out
            } else {
                envelope = 1.0
            }

            // Apply some gentle filtering to make it more natural
            let filteredSignal = combinedSignal * (1.0 - time * 0.1) // Slight high-frequency rolloff over time

            samples[i] = Float(filteredSignal) * envelope * volume * 0.4 // Reasonable volume
        }

        // Play the generated murmuration
        playGeneratedBuffer(audioBuffer)
    }
    
    private func playGeneratedBuffer(_ buffer: AVAudioPCMBuffer) {
        let audioEngine = AVAudioEngine()
        let playerNode = AVAudioPlayerNode()
        
        audioEngine.attach(playerNode)
        audioEngine.connect(playerNode, to: audioEngine.mainMixerNode, format: buffer.format)
        
        do {
            try audioEngine.start()
            isPlaying = true
            
            playerNode.scheduleBuffer(buffer, at: nil, options: [], completionHandler: {
                DispatchQueue.main.async {
                    self.isPlaying = false
                    audioEngine.stop()
                }
            })
            
            playerNode.play()
            
        } catch {
            print("Error playing generated cicada sound: \(error)")
            isPlaying = false
        }
    }
    
    private func fadeOut() {
        guard let player = audioPlayer, player.isPlaying else { return }
        
        let fadeTime: TimeInterval = 1.0
        let steps = 20
        let volumeStep = player.volume / Float(steps)
        let timeStep = fadeTime / Double(steps)
        
        for i in 1...steps {
            DispatchQueue.main.asyncAfter(deadline: .now() + timeStep * Double(i)) {
                player.volume = max(0, player.volume - volumeStep)
                if i == steps {
                    player.stop()
                    self.isPlaying = false
                }
            }
        }
    }
    
    func stop() {
        audioPlayer?.stop()
        isPlaying = false
    }
    
    func setVolume(_ newVolume: Float) {
        volume = max(0.0, min(1.0, newVolume))
        audioPlayer?.volume = volume
    }
}

extension AudioService: AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        isPlaying = false
    }
    
    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        print("Audio player decode error: \(error?.localizedDescription ?? "Unknown error")")
        isPlaying = false
    }
}
