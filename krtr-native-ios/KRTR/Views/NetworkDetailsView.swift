/**
 * Network Details View - Shows detailed mesh network information
 * Provides real-time network status, peer information, and controls
 */

import SwiftUI

struct NetworkDetailsView: View {
    @ObservedObject var meshService: BluetoothMeshService
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Network Status Section
                    networkStatusSection
                    
                    // Connected Peers Section
                    connectedPeersSection
                    
                    // Network Statistics Section
                    networkStatsSection
                    
                    // Network Controls Section
                    networkControlsSection
                }
                .padding()
            }
            .navigationTitle("Network Details")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - Network Status Section
    private var networkStatusSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Network Status")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                StatusCard(
                    title: "Connection",
                    value: meshService.isConnected ? "Connected" : "Disconnected",
                    icon: meshService.isConnected ? "wifi" : "wifi.slash",
                    color: meshService.isConnected ? .green : .red
                )
                
                StatusCard(
                    title: "Peers",
                    value: "\(meshService.connectedPeers.count)",
                    icon: "person.3.fill",
                    color: .blue
                )
                
                StatusCard(
                    title: "Scanning",
                    value: meshService.isScanning ? "Active" : "Inactive",
                    icon: "radar",
                    color: meshService.isScanning ? .green : .gray
                )
                
                StatusCard(
                    title: "Advertising",
                    value: meshService.isAdvertising ? "Active" : "Inactive",
                    icon: "antenna.radiowaves.left.and.right",
                    color: meshService.isAdvertising ? .green : .gray
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - Connected Peers Section
    private var connectedPeersSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Connected Peers")
                .font(.headline)
                .fontWeight(.semibold)
            
            if meshService.connectedPeers.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "person.3.slash")
                        .font(.system(size: 40))
                        .foregroundColor(.gray)
                    
                    Text("No peers connected")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("Make sure Bluetooth is enabled and other KRTR devices are nearby")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(meshService.connectedPeers.enumerated()), id: \.offset) { index, peer in
                        PeerCard(peer: peer, index: index)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - Network Statistics Section
    private var networkStatsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Network Statistics")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                StatCard(
                    title: "Messages Sent",
                    value: "\(meshService.messagesSent)",
                    icon: "arrow.up.circle"
                )
                
                StatCard(
                    title: "Messages Received",
                    value: "\(meshService.messagesReceived)",
                    icon: "arrow.down.circle"
                )
                
                StatCard(
                    title: "Uptime",
                    value: formatUptime(meshService.connectionStartTime),
                    icon: "clock"
                )
                
                StatCard(
                    title: "Signal Strength",
                    value: "Strong", // This would be calculated from RSSI
                    icon: "wifi"
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - Network Controls Section
    private var networkControlsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Network Controls")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                Button(action: {
                    if meshService.isScanning {
                        meshService.stopScanning()
                    } else {
                        meshService.startScanning()
                    }
                }) {
                    HStack {
                        Image(systemName: meshService.isScanning ? "stop.circle" : "play.circle")
                        Text(meshService.isScanning ? "Stop Scanning" : "Start Scanning")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(meshService.isScanning ? Color.red : Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                
                Button(action: {
                    meshService.refreshConnections()
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text("Refresh Connections")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - Helper Methods
    private func formatUptime(_ startTime: Date?) -> String {
        guard let startTime = startTime else { return "N/A" }
        let interval = Date().timeIntervalSince(startTime)
        let hours = Int(interval) / 3600
        let minutes = Int(interval) % 3600 / 60
        return "\(hours)h \(minutes)m"
    }
}

// MARK: - Supporting Components
struct StatusCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct PeerCard: View {
    let peer: String
    let index: Int
    
    var body: some View {
        HStack {
            Circle()
                .fill(Color.green)
                .frame(width: 12, height: 12)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("Peer \(index + 1)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(peer)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text("Connected")
                .font(.caption)
                .foregroundColor(.green)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.green.opacity(0.1))
                .cornerRadius(4)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    NetworkDetailsView(meshService: BluetoothMeshService())
}
