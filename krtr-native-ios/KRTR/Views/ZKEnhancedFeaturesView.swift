//
// ZKEnhancedFeaturesView.swift
// KRTR
//
// This is free and unencumbered software released into the public domain.
// For more information, see <https://unlicense.org>
//

import SwiftUI
import Foundation

struct ZKEnhancedFeaturesView: View {
    @StateObject private var zkService = MockZKService()
    @State private var isAnonymousMode = false
    @State private var trustedGroupsEnabled = true
    @State private var showingPrivacyInfo = false
    @State private var showingGroupInfo = false
    @State private var showingLocationInfo = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection

                    // Privacy Controls
                    privacyControlsSection

                    // Trusted Groups
                    trustedGroupsSection

                    // Location Privacy
                    locationPrivacySection

                    // Privacy Tips
                    privacyTipsSection

                    Spacer(minLength: 50)
                }
                .padding()
            }
            .navigationTitle("Privacy Features")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingPrivacyInfo) {
            PrivacyInfoSheet()
        }
        .sheet(isPresented: $showingGroupInfo) {
            TrustedGroupsInfoSheet()
        }
        .sheet(isPresented: $showingLocationInfo) {
            LocationPrivacyInfoSheet()
        }
    }

    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "shield.lefthalf.filled")
                    .foregroundColor(.blue)
                    .font(.title2)
                Text("Enhanced Privacy")
                    .font(.title2)
                    .fontWeight(.semibold)
            }

            Text("Advanced privacy features to keep your conversations secure and anonymous")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var privacyControlsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "eye.slash.fill")
                    .foregroundColor(.purple)
                    .font(.title3)
                Text("Anonymous Mode")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Hide Your Identity")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Text("Messages appear without your name or device info")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Toggle("", isOn: $isAnonymousMode)
                        .toggleStyle(SwitchToggleStyle(tint: .purple))
                }

                if isAnonymousMode {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.caption)
                        Text("Anonymous mode active - your identity is hidden")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                    .padding(.top, 4)
                }

                Button(action: { showingPrivacyInfo = true }) {
                    HStack {
                        Image(systemName: "info.circle")
                        Text("How Anonymous Mode Works")
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }

    private var trustedGroupsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "person.3.fill")
                    .foregroundColor(.green)
                    .font(.title3)
                Text("Trusted Groups")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Verified Contacts Only")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Text("Only receive messages from people you trust")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Toggle("", isOn: $trustedGroupsEnabled)
                        .toggleStyle(SwitchToggleStyle(tint: .green))
                }

                if trustedGroupsEnabled {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)
                            Text("Trusted groups enabled - filtering unknown contacts")
                                .font(.caption)
                                .foregroundColor(.green)
                        }

                        HStack {
                            Image(systemName: "person.badge.plus")
                                .foregroundColor(.blue)
                                .font(.caption)
                            Text("3 trusted contacts")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.top, 4)
                }

                Button(action: { showingGroupInfo = true }) {
                    HStack {
                        Image(systemName: "person.badge.plus")
                        Text("Manage Trusted Contacts")
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }

    private var locationPrivacySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "location.slash.fill")
                    .foregroundColor(.orange)
                    .font(.title3)
                Text("Location Privacy")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                    Text("Location data is never stored or shared")
                        .font(.caption)
                        .foregroundColor(.green)
                }

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                    Text("Mesh connections use encrypted proximity only")
                        .font(.caption)
                        .foregroundColor(.green)
                }

                Button(action: { showingLocationInfo = true }) {
                    HStack {
                        Image(systemName: "info.circle")
                        Text("Learn About Location Privacy")
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }

    private var privacyTipsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                    .font(.title3)
                Text("Privacy Tips")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            VStack(alignment: .leading, spacing: 12) {
                PrivacyTipRow(
                    icon: "shield.fill",
                    title: "Use Anonymous Mode",
                    description: "For maximum privacy in public spaces"
                )

                PrivacyTipRow(
                    icon: "person.3.fill",
                    title: "Build Trusted Groups",
                    description: "Add people you know to filter messages"
                )

                PrivacyTipRow(
                    icon: "wifi.slash",
                    title: "Works Offline",
                    description: "No internet required - mesh network only"
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct PrivacyTipRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .font(.subheadline)
                .frame(width: 20)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

// Info Sheets
struct PrivacyInfoSheet: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Anonymous Mode")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("When anonymous mode is enabled:")
                        .font(.headline)

                    VStack(alignment: .leading, spacing: 12) {
                        BulletPoint(text: "Your messages appear without any identifying information")
                        BulletPoint(text: "Device fingerprints are randomized")
                        BulletPoint(text: "Message patterns are obscured")
                        BulletPoint(text: "You can still receive replies through encrypted channels")
                    }

                    Text("Perfect for:")
                        .font(.headline)
                        .padding(.top)

                    VStack(alignment: .leading, spacing: 8) {
                        BulletPoint(text: "Public spaces and events")
                        BulletPoint(text: "Sensitive conversations")
                        BulletPoint(text: "Avoiding unwanted contact")
                    }
                }
                .padding()
            }
            .navigationTitle("Anonymous Mode")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct TrustedGroupsInfoSheet: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Trusted Groups")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("Trusted groups help you filter messages from unknown contacts.")
                        .font(.body)

                    Text("How it works:")
                        .font(.headline)
                        .padding(.top)

                    VStack(alignment: .leading, spacing: 12) {
                        BulletPoint(text: "Add people you know to your trusted group")
                        BulletPoint(text: "Messages from trusted contacts appear normally")
                        BulletPoint(text: "Unknown messages are filtered or marked")
                        BulletPoint(text: "You can still see filtered messages if needed")
                    }

                    Text("Adding trusted contacts:")
                        .font(.headline)
                        .padding(.top)

                    VStack(alignment: .leading, spacing: 8) {
                        BulletPoint(text: "Exchange contact codes in person")
                        BulletPoint(text: "Verify identity through shared secrets")
                        BulletPoint(text: "Remove contacts anytime")
                    }
                }
                .padding()
            }
            .navigationTitle("Trusted Groups")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct LocationPrivacyInfoSheet: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Location Privacy")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("KRTR protects your location privacy:")
                        .font(.body)

                    VStack(alignment: .leading, spacing: 12) {
                        BulletPoint(text: "No GPS or location services used")
                        BulletPoint(text: "Bluetooth proximity only (no exact location)")
                        BulletPoint(text: "No location data stored on device")
                        BulletPoint(text: "No location data transmitted")
                    }

                    Text("How mesh networking works:")
                        .font(.headline)
                        .padding(.top)

                    VStack(alignment: .leading, spacing: 12) {
                        BulletPoint(text: "Devices detect each other through Bluetooth")
                        BulletPoint(text: "Only knows 'nearby' or 'not nearby'")
                        BulletPoint(text: "Messages hop through the mesh network")
                        BulletPoint(text: "No central servers or tracking")
                    }
                }
                .padding()
            }
            .navigationTitle("Location Privacy")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct BulletPoint: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Text("•")
                .font(.body)
                .foregroundColor(.blue)
            Text(text)
                .font(.body)
        }
    }
}
