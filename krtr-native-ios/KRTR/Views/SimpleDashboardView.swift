/**
 * KRTR Simple Dashboard - Clean, functional interface
 * Combines chat, mesh status, and essential controls in one view
 * Replaces complex multi-tab interface with streamlined design
 */

import SwiftUI

struct SimpleDashboardView: View {
    @StateObject private var chatViewModel = ChatViewModel()
    @StateObject private var meshService = BluetoothMeshService()
    @State private var messageText = ""
    @State private var showingSettings = false
    @State private var showingNetworkDetails = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with network status
                headerView
                
                Divider()
                
                // Main chat area
                chatArea
                
                Divider()
                
                // Message input
                messageInputView
            }
            .navigationTitle("KRTR Mesh")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    networkStatusButton
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    settingsButton
                }
            }
        }
        .environmentObject(chatViewModel)
        .environmentObject(meshService)
        .onAppear {
            setupMeshService()
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showingNetworkDetails) {
            NetworkDetailsView(meshService: meshService)
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            // Channel/Chat info
            VStack(alignment: .leading, spacing: 2) {
                Text(chatViewModel.currentChannel.isEmpty ? "General Chat" : chatViewModel.currentChannel)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("\(chatViewModel.messages.count) messages")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Network status indicator
            HStack(spacing: 8) {
                Circle()
                    .fill(meshService.isConnected ? Color.green : Color.red)
                    .frame(width: 8, height: 8)
                
                Text("\(meshService.connectedPeers.count) peers")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if meshService.isScanning {
                    ProgressView()
                        .scaleEffect(0.6)
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
    
    // MARK: - Chat Area
    private var chatArea: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 8) {
                    ForEach(chatViewModel.messages) { message in
                        MessageBubble(message: message)
                            .id(message.id)
                    }
                    
                    if chatViewModel.messages.isEmpty {
                        emptyStateView
                    }
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
            }
            .onChange(of: chatViewModel.messages.count) { _ in
                if let lastMessage = chatViewModel.messages.last {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "message.circle")
                .font(.system(size: 50))
                .foregroundColor(.gray)
            
            Text("Welcome to KRTR Mesh")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Start chatting with nearby devices using Bluetooth mesh networking")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            if !meshService.isConnected {
                Button("Start Networking") {
                    meshService.startScanning()
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    // MARK: - Message Input
    private var messageInputView: some View {
        HStack(spacing: 12) {
            TextField("Type a message...", text: $messageText, axis: .vertical)
                .textFieldStyle(.roundedBorder)
                .lineLimit(1...4)
                .onSubmit {
                    sendMessage()
                }
            
            Button(action: sendMessage) {
                Image(systemName: "arrow.up.circle.fill")
                    .font(.title2)
                    .foregroundColor(messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? .gray : .blue)
            }
            .disabled(messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    // MARK: - Toolbar Buttons
    private var networkStatusButton: some View {
        Button(action: { showingNetworkDetails = true }) {
            HStack(spacing: 4) {
                Image(systemName: "network")
                    .foregroundColor(meshService.isConnected ? .green : .red)
                Text("\(meshService.connectedPeers.count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var settingsButton: some View {
        Button(action: { showingSettings = true }) {
            Image(systemName: "gear")
        }
    }
    
    // MARK: - Helper Methods
    private func sendMessage() {
        let content = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !content.isEmpty else { return }
        
        chatViewModel.sendMessage(content)
        messageText = ""
    }
    
    private func setupMeshService() {
        chatViewModel.meshService = meshService
        meshService.delegate = chatViewModel
        meshService.startScanning()
    }
}

// MARK: - Message Bubble Component
struct MessageBubble: View {
    let message: KRTRMessage
    
    private var isOwnMessage: Bool {
        // This would need to be determined based on the sender
        return message.sender == "You" // Simplified for now
    }
    
    var body: some View {
        HStack {
            if isOwnMessage {
                Spacer()
            }
            
            VStack(alignment: isOwnMessage ? .trailing : .leading, spacing: 4) {
                if !isOwnMessage {
                    Text(message.sender)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Text(message.content)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(isOwnMessage ? Color.blue : Color(.systemGray5))
                    .foregroundColor(isOwnMessage ? .white : .primary)
                    .cornerRadius(16)
                
                Text(formatTime(message.timestamp))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            if !isOwnMessage {
                Spacer()
            }
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Preview
#Preview {
    SimpleDashboardView()
}
