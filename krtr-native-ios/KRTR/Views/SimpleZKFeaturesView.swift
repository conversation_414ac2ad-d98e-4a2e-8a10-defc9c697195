/**
 * Simple ZK Features View - Intuitive privacy controls
 * Replaces complex ZK interface with straightforward user experience
 */

import SwiftUI

struct SimpleZKFeaturesView: View {
    @StateObject private var zkService = MockZKService()
    @State private var privacyMode: PrivacyMode = .standard
    @State private var isAnonymous = false
    @State private var trustedContacts: [TrustedContact] = []
    @State private var showingContactPicker = false
    @State private var showingSuccessAlert = false
    @State private var successMessage = ""
    
    enum PrivacyMode: String, CaseIterable {
        case standard = "Standard"
        case anonymous = "Anonymous"
        case trustedOnly = "Trusted Only"
        
        var description: String {
            switch self {
            case .standard: return "Normal encrypted chat"
            case .anonymous: return "Hide your identity"
            case .trustedOnly: return "Only verified contacts"
            }
        }
        
        var icon: String {
            switch self {
            case .standard: return "message.fill"
            case .anonymous: return "eye.slash.fill"
            case .trustedOnly: return "checkmark.shield.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .standard: return .blue
            case .anonymous: return .purple
            case .trustedOnly: return .green
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Current Status
                    currentStatusSection
                    
                    // Privacy Mode Selector
                    privacyModeSection
                    
                    // Trusted Contacts
                    trustedContactsSection
                    
                    // Quick Actions
                    quickActionsSection
                }
                .padding()
            }
            .navigationTitle("Privacy & Trust")
            .navigationBarTitleDisplayMode(.large)
        }
        .onAppear {
            setupInitialData()
        }
        .sheet(isPresented: $showingContactPicker) {
            ContactPickerView { contact in
                trustedContacts.append(contact)
            }
        }
        .alert("Success!", isPresented: $showingSuccessAlert) {
            Button("OK") { }
        } message: {
            Text(successMessage)
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "shield.lefthalf.filled")
                .font(.system(size: 60))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            Text("Stay Private & Safe")
                .font(.title)
                .fontWeight(.bold)
            
            Text("Control who can see your messages and identity while chatting")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - Current Status Section
    private var currentStatusSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Your Current Privacy")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 16) {
                Image(systemName: privacyMode.icon)
                    .font(.title2)
                    .foregroundColor(privacyMode.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(privacyMode.rawValue)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Text(privacyMode.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if isAnonymous {
                    Text("🕶️ Anonymous")
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.purple.opacity(0.2))
                        .foregroundColor(.purple)
                        .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - Privacy Mode Section
    private var privacyModeSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Choose Privacy Level")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                ForEach(PrivacyMode.allCases, id: \.self) { mode in
                    PrivacyModeCard(
                        mode: mode,
                        isSelected: privacyMode == mode
                    ) {
                        selectPrivacyMode(mode)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - Trusted Contacts Section
    private var trustedContactsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Trusted Contacts")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Add Contact") {
                    showingContactPicker = true
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.small)
            }
            
            if trustedContacts.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "person.badge.plus")
                        .font(.system(size: 40))
                        .foregroundColor(.gray)
                    
                    Text("No trusted contacts yet")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("Add contacts you trust to enable verified-only mode")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(trustedContacts, id: \.id) { contact in
                        TrustedContactCard(contact: contact)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                Button(action: toggleAnonymousMode) {
                    HStack {
                        Image(systemName: isAnonymous ? "eye.fill" : "eye.slash.fill")
                        Text(isAnonymous ? "Show My Identity" : "Go Anonymous")
                        Spacer()
                    }
                    .padding()
                    .background(isAnonymous ? Color.blue : Color.purple)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                
                Button(action: generateTrustProof) {
                    HStack {
                        Image(systemName: "checkmark.shield.fill")
                        Text("Generate Trust Proof")
                        Spacer()
                    }
                    .padding()
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - Helper Methods
    private func setupInitialData() {
        // Initialize with some sample trusted contacts
        trustedContacts = [
            TrustedContact(id: "1", name: "Alice", isVerified: true),
            TrustedContact(id: "2", name: "Bob", isVerified: false)
        ]
    }
    
    private func selectPrivacyMode(_ mode: PrivacyMode) {
        privacyMode = mode
        
        switch mode {
        case .standard:
            isAnonymous = false
            successMessage = "Privacy set to Standard. Your messages are encrypted but your identity is visible."
        case .anonymous:
            isAnonymous = true
            successMessage = "Privacy set to Anonymous. Your identity is hidden using zero-knowledge proofs."
        case .trustedOnly:
            isAnonymous = false
            successMessage = "Privacy set to Trusted Only. You can only chat with verified contacts."
        }
        
        showingSuccessAlert = true
    }
    
    private func toggleAnonymousMode() {
        isAnonymous.toggle()
        
        if isAnonymous {
            privacyMode = .anonymous
            successMessage = "🕶️ You're now anonymous! Your identity is hidden using zero-knowledge proofs."
        } else {
            privacyMode = .standard
            successMessage = "👋 You're now visible! Others can see your identity."
        }
        
        showingSuccessAlert = true
    }
    
    private func generateTrustProof() {
        // Simulate generating a trust proof
        successMessage = "🛡️ Trust proof generated! You can now access verified-only channels."
        showingSuccessAlert = true
    }
}

// MARK: - Supporting Data Models
struct TrustedContact {
    let id: String
    let name: String
    let isVerified: Bool
}

// MARK: - Supporting Components
struct PrivacyModeCard: View {
    let mode: SimpleZKFeaturesView.PrivacyMode
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 16) {
                Image(systemName: mode.icon)
                    .font(.title2)
                    .foregroundColor(mode.color)
                    .frame(width: 30)

                VStack(alignment: .leading, spacing: 4) {
                    Text(mode.rawValue)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(mode.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(mode.color)
                }
            }
            .padding()
            .background(isSelected ? mode.color.opacity(0.1) : Color(.systemGray6))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? mode.color : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(.plain)
    }
}

struct TrustedContactCard: View {
    let contact: TrustedContact

    var body: some View {
        HStack(spacing: 12) {
            Circle()
                .fill(contact.isVerified ? Color.green : Color.orange)
                .frame(width: 12, height: 12)

            VStack(alignment: .leading, spacing: 2) {
                Text(contact.name)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(contact.isVerified ? "Verified" : "Pending verification")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            if contact.isVerified {
                Image(systemName: "checkmark.shield.fill")
                    .foregroundColor(.green)
            } else {
                Image(systemName: "clock.fill")
                    .foregroundColor(.orange)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct ContactPickerView: View {
    let onContactSelected: (TrustedContact) -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Contact Picker")
                    .font(.title2)
                    .padding()

                Text("This would show a list of available contacts to add as trusted.")
                    .foregroundColor(.secondary)
                    .padding()

                Spacer()

                Button("Add Sample Contact") {
                    let newContact = TrustedContact(
                        id: UUID().uuidString,
                        name: "New Contact",
                        isVerified: false
                    )
                    onContactSelected(newContact)
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
                .padding()
            }
            .navigationTitle("Add Contact")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    SimpleZKFeaturesView()
}
