name: Deploy Landing Page to Vercel

on:
  push:
    branches: [ main ]
    paths: 
      - 'landing-page/**'
  pull_request:
    branches: [ main ]
    paths: 
      - 'landing-page/**'

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: landing-page/package-lock.json

    - name: Install dependencies
      run: |
        cd landing-page
        npm ci

    - name: Run type check
      run: |
        cd landing-page
        npm run type-check

    - name: Run linting
      run: |
        cd landing-page
        npm run lint

    - name: Build project
      run: |
        cd landing-page
        npm run build

    - name: Deploy to Vercel (Production)
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./landing-page
        vercel-args: '--prod'

    - name: Deploy to Vercel (Preview)
      if: github.event_name == 'pull_request'
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./landing-page

    - name: Comment PR with preview URL
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const { data: comments } = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
          });
          
          const botComment = comments.find(comment => 
            comment.user.type === 'Bot' && comment.body.includes('Preview deployment')
          );
          
          const commentBody = `🚀 **Preview deployment ready!**
          
          Your landing page changes have been deployed to a preview environment.
          
          **Preview URL:** Will be available after Vercel deployment completes
          
          This preview will be updated automatically when you push new commits to this PR.`;
          
          if (botComment) {
            await github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: botComment.id,
              body: commentBody
            });
          } else {
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: commentBody
            });
          }
